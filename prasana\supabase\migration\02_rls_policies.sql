-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- ROLE-BASED ACCESS CONTROL: USER, ADMIN, SUPERADMIN
-- =====================================================

-- Helper function to get user's role level
CREATE OR REPLACE FUNCTION get_user_role_level(user_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    role_level INTEGER;
BEGIN
    SELECT sr.level INTO role_level
    FROM user_role_assignments ura
    JOIN system_roles sr ON ura.system_role_id = sr.id
    WHERE ura.user_id = user_uuid 
    AND ura.is_active = true
    ORDER BY sr.level DESC
    LIMIT 1;
    
    RETURN COALESCE(role_level, 1); -- Default to User level (1)
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is team member of a project
CREATE OR REPLACE FUNCTION is_project_team_member(user_uuid UUID, project_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    is_member BOOLEAN;
BEGIN
    SELECT EXISTS(
        SELECT 1 
        FROM project_members pm
        JOIN employees e ON pm.employee_id = e.id
        WHERE e.user_id = user_uuid 
        AND pm.project_id = project_uuid 
        AND pm.is_active = true
    ) INTO is_member;
    
    RETURN is_member;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to get employee ID from user ID
CREATE OR REPLACE FUNCTION get_employee_id(user_uuid UUID)
RETURNS UUID AS $$
DECLARE
    emp_id UUID;
BEGIN
    SELECT id INTO emp_id FROM employees WHERE user_id = user_uuid;
    RETURN emp_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- ENABLE RLS ON ALL TABLES
-- =====================================================

ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_role_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE time_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE badges ENABLE ROW LEVEL SECURITY;
ALTER TABLE employee_badges ENABLE ROW LEVEL SECURITY;
ALTER TABLE leave_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE leave_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE employee_leave_balances ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE payroll ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_goals ENABLE ROW LEVEL SECURITY;
ALTER TABLE employee_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE holidays ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- USERS TABLE POLICIES
-- =====================================================

-- Users can view their own profile and team members
CREATE POLICY "users_select_policy" ON users FOR SELECT
USING (
    auth.uid() = id OR -- Own profile
    get_user_role_level(auth.uid()) >= 2 -- Admin+ can see all users
);

-- Users can update their own profile
CREATE POLICY "users_update_policy" ON users FOR UPDATE
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- Only superadmins can insert/delete users
CREATE POLICY "users_insert_policy" ON users FOR INSERT
WITH CHECK (get_user_role_level(auth.uid()) >= 3);

CREATE POLICY "users_delete_policy" ON users FOR DELETE
USING (get_user_role_level(auth.uid()) >= 3);

-- =====================================================
-- EMPLOYEE TABLE POLICIES (HRMS)
-- =====================================================

-- USER ROLE: View team members, edit own profile
-- ADMIN ROLE: View and manage team members
-- SUPERADMIN ROLE: View and manage all employees
CREATE POLICY "employees_select_policy" ON employees FOR SELECT
USING (
    user_id = auth.uid() OR -- Own profile
    (get_user_role_level(auth.uid()) >= 2 AND team_id IN (
        SELECT team_id FROM employees WHERE user_id = auth.uid()
    )) OR -- Admin can see team members
    get_user_role_level(auth.uid()) >= 3 -- Superadmin sees all
);

-- Users can update their own profile, Admins can update team members, Superadmins can update all
CREATE POLICY "employees_update_policy" ON employees FOR UPDATE
USING (
    user_id = auth.uid() OR -- Own profile
    (get_user_role_level(auth.uid()) >= 2 AND team_id IN (
        SELECT team_id FROM employees WHERE user_id = auth.uid()
    )) OR -- Admin can update team members
    get_user_role_level(auth.uid()) >= 3 -- Superadmin updates all
)
WITH CHECK (
    user_id = auth.uid() OR
    get_user_role_level(auth.uid()) >= 2
);

-- Only Admins+ can create employees
CREATE POLICY "employees_insert_policy" ON employees FOR INSERT
WITH CHECK (get_user_role_level(auth.uid()) >= 2);

-- Only Superadmins can delete employees
CREATE POLICY "employees_delete_policy" ON employees FOR DELETE
USING (get_user_role_level(auth.uid()) >= 3);

-- =====================================================
-- PROJECT MANAGEMENT POLICIES
-- =====================================================

-- USER ROLE: View projects they have access to
-- ADMIN ROLE: Create, edit, and manage projects
-- SUPERADMIN ROLE: Full access to all projects
CREATE POLICY "projects_select_policy" ON projects FOR SELECT
USING (
    is_project_team_member(auth.uid(), id) OR -- Team member
    get_user_role_level(auth.uid()) >= 2 -- Admin+ sees all
);

-- Only Admins+ can create/update projects
CREATE POLICY "projects_insert_policy" ON projects FOR INSERT
WITH CHECK (get_user_role_level(auth.uid()) >= 2);

CREATE POLICY "projects_update_policy" ON projects FOR UPDATE
USING (get_user_role_level(auth.uid()) >= 2)
WITH CHECK (get_user_role_level(auth.uid()) >= 2);

-- Only Superadmins can delete projects
CREATE POLICY "projects_delete_policy" ON projects FOR DELETE
USING (get_user_role_level(auth.uid()) >= 3);

-- =====================================================
-- PROJECT MEMBERS POLICIES
-- =====================================================

-- Users can see project members for projects they're part of
CREATE POLICY "project_members_select_policy" ON project_members FOR SELECT
USING (
    is_project_team_member(auth.uid(), project_id) OR
    get_user_role_level(auth.uid()) >= 2
);

-- Only Admins+ can add/remove project members
CREATE POLICY "project_members_insert_policy" ON project_members FOR INSERT
WITH CHECK (get_user_role_level(auth.uid()) >= 2);

CREATE POLICY "project_members_update_policy" ON project_members FOR UPDATE
USING (get_user_role_level(auth.uid()) >= 2)
WITH CHECK (get_user_role_level(auth.uid()) >= 2);

CREATE POLICY "project_members_delete_policy" ON project_members FOR DELETE
USING (get_user_role_level(auth.uid()) >= 2);

-- =====================================================
-- TIME ENTRIES/TIMESHEET POLICIES
-- =====================================================

-- USER ROLE: View all team timesheets, edit own entries
-- ADMIN ROLE: View and manage team timesheets (approve/reject)
-- SUPERADMIN ROLE: Full access to all timesheets
CREATE POLICY "time_entries_select_policy" ON time_entries FOR SELECT
USING (
    employee_id = get_employee_id(auth.uid()) OR -- Own entries
    employee_id IN (
        SELECT e.id FROM employees e 
        WHERE e.team_id IN (
            SELECT team_id FROM employees WHERE user_id = auth.uid()
        )
    ) OR -- Team members' entries (USER role can view team timesheets)
    get_user_role_level(auth.uid()) >= 2 -- Admin+ sees all
);

-- Users can create/update their own time entries
CREATE POLICY "time_entries_insert_policy" ON time_entries FOR INSERT
WITH CHECK (
    employee_id = get_employee_id(auth.uid()) OR
    get_user_role_level(auth.uid()) >= 2
);

CREATE POLICY "time_entries_update_policy" ON time_entries FOR UPDATE
USING (
    employee_id = get_employee_id(auth.uid()) OR -- Own entries
    get_user_role_level(auth.uid()) >= 2 -- Admin+ can approve/reject
)
WITH CHECK (
    employee_id = get_employee_id(auth.uid()) OR
    get_user_role_level(auth.uid()) >= 2
);

-- Only Admins+ can delete time entries
CREATE POLICY "time_entries_delete_policy" ON time_entries FOR DELETE
USING (get_user_role_level(auth.uid()) >= 2);

-- =====================================================
-- BADGES & CERTIFICATIONS POLICIES
-- =====================================================

-- Everyone can view badges
CREATE POLICY "badges_select_policy" ON badges FOR SELECT
USING (true);

-- Only Admins+ can create/manage badges
CREATE POLICY "badges_insert_policy" ON badges FOR INSERT
WITH CHECK (get_user_role_level(auth.uid()) >= 2);

CREATE POLICY "badges_update_policy" ON badges FOR UPDATE
USING (get_user_role_level(auth.uid()) >= 2)
WITH CHECK (get_user_role_level(auth.uid()) >= 2);

CREATE POLICY "badges_delete_policy" ON badges FOR DELETE
USING (get_user_role_level(auth.uid()) >= 2);

-- =====================================================
-- EMPLOYEE BADGES POLICIES
-- =====================================================

-- USER ROLE: View their own badges and expiry dates
-- ADMIN ROLE: Assign badges to team members
-- SUPERADMIN ROLE: Assign badges to all employees
CREATE POLICY "employee_badges_select_policy" ON employee_badges FOR SELECT
USING (
    employee_id = get_employee_id(auth.uid()) OR -- Own badges
    get_user_role_level(auth.uid()) >= 2 -- Admin+ sees all
);

-- Only Admins+ can assign badges
CREATE POLICY "employee_badges_insert_policy" ON employee_badges FOR INSERT
WITH CHECK (get_user_role_level(auth.uid()) >= 2);

-- Only Admins+ can update badge assignments
CREATE POLICY "employee_badges_update_policy" ON employee_badges FOR UPDATE
USING (get_user_role_level(auth.uid()) >= 2)
WITH CHECK (get_user_role_level(auth.uid()) >= 2);

-- Only Admins+ can revoke badges
CREATE POLICY "employee_badges_delete_policy" ON employee_badges FOR DELETE
USING (get_user_role_level(auth.uid()) >= 2);

-- =====================================================
-- LEAVE MANAGEMENT POLICIES
-- =====================================================

-- Everyone can view leave types
CREATE POLICY "leave_types_select_policy" ON leave_types FOR SELECT
USING (true);

-- Only Superadmins can manage leave types
CREATE POLICY "leave_types_insert_policy" ON leave_types FOR INSERT
WITH CHECK (get_user_role_level(auth.uid()) >= 3);

CREATE POLICY "leave_types_update_policy" ON leave_types FOR UPDATE
USING (get_user_role_level(auth.uid()) >= 3)
WITH CHECK (get_user_role_level(auth.uid()) >= 3);

CREATE POLICY "leave_types_delete_policy" ON leave_types FOR DELETE
USING (get_user_role_level(auth.uid()) >= 3);

-- =====================================================
-- LEAVE APPLICATIONS POLICIES
-- =====================================================

-- USER ROLE: View own leave applications and history
-- ADMIN ROLE: View team leave applications
-- SUPERADMIN ROLE: Approve/reject all leave applications
CREATE POLICY "leave_applications_select_policy" ON leave_applications FOR SELECT
USING (
    employee_id = get_employee_id(auth.uid()) OR -- Own applications
    (get_user_role_level(auth.uid()) >= 2 AND employee_id IN (
        SELECT e.id FROM employees e
        WHERE e.team_id IN (
            SELECT team_id FROM employees WHERE user_id = auth.uid()
        )
    )) OR -- Admin sees team applications
    get_user_role_level(auth.uid()) >= 3 -- Superadmin sees all
);

-- Users can apply for leave
CREATE POLICY "leave_applications_insert_policy" ON leave_applications FOR INSERT
WITH CHECK (
    employee_id = get_employee_id(auth.uid()) OR
    get_user_role_level(auth.uid()) >= 2
);

-- Users can update their own applications (before approval), Superadmins can approve/reject
CREATE POLICY "leave_applications_update_policy" ON leave_applications FOR UPDATE
USING (
    (employee_id = get_employee_id(auth.uid()) AND status = 'pending') OR -- Own pending applications
    get_user_role_level(auth.uid()) >= 3 -- Superadmin can approve/reject
)
WITH CHECK (
    employee_id = get_employee_id(auth.uid()) OR
    get_user_role_level(auth.uid()) >= 3
);

-- Only Superadmins can delete leave applications
CREATE POLICY "leave_applications_delete_policy" ON leave_applications FOR DELETE
USING (get_user_role_level(auth.uid()) >= 3);

-- =====================================================
-- ATTENDANCE POLICIES
-- =====================================================

-- USER ROLE: View own attendance
-- ADMIN ROLE: View team attendance
-- SUPERADMIN ROLE: View all attendance
CREATE POLICY "attendance_select_policy" ON attendance FOR SELECT
USING (
    employee_id = get_employee_id(auth.uid()) OR -- Own attendance
    (get_user_role_level(auth.uid()) >= 2 AND employee_id IN (
        SELECT e.id FROM employees e
        WHERE e.team_id IN (
            SELECT team_id FROM employees WHERE user_id = auth.uid()
        )
    )) OR -- Admin sees team attendance
    get_user_role_level(auth.uid()) >= 3 -- Superadmin sees all
);

-- Users can clock in/out (create their own attendance)
CREATE POLICY "attendance_insert_policy" ON attendance FOR INSERT
WITH CHECK (
    employee_id = get_employee_id(auth.uid()) OR
    get_user_role_level(auth.uid()) >= 2
);

-- Users can update their own attendance, Admins+ can approve
CREATE POLICY "attendance_update_policy" ON attendance FOR UPDATE
USING (
    employee_id = get_employee_id(auth.uid()) OR
    get_user_role_level(auth.uid()) >= 2
)
WITH CHECK (
    employee_id = get_employee_id(auth.uid()) OR
    get_user_role_level(auth.uid()) >= 2
);

-- Only Admins+ can delete attendance records
CREATE POLICY "attendance_delete_policy" ON attendance FOR DELETE
USING (get_user_role_level(auth.uid()) >= 2);

-- =====================================================
-- PAYROLL POLICIES
-- =====================================================

-- USER ROLE: View own payroll information
-- SUPERADMIN ROLE: Process payroll for all employees
CREATE POLICY "payroll_select_policy" ON payroll FOR SELECT
USING (
    employee_id = get_employee_id(auth.uid()) OR -- Own payroll
    get_user_role_level(auth.uid()) >= 3 -- Superadmin sees all
);

-- Only Superadmins can create/manage payroll
CREATE POLICY "payroll_insert_policy" ON payroll FOR INSERT
WITH CHECK (get_user_role_level(auth.uid()) >= 3);

CREATE POLICY "payroll_update_policy" ON payroll FOR UPDATE
USING (get_user_role_level(auth.uid()) >= 3)
WITH CHECK (get_user_role_level(auth.uid()) >= 3);

CREATE POLICY "payroll_delete_policy" ON payroll FOR DELETE
USING (get_user_role_level(auth.uid()) >= 3);

-- =====================================================
-- PERFORMANCE MANAGEMENT POLICIES
-- =====================================================

-- USER ROLE: View own performance reviews and goals
-- ADMIN ROLE: View team performance data
-- SUPERADMIN ROLE: Access all performance data
CREATE POLICY "performance_reviews_select_policy" ON performance_reviews FOR SELECT
USING (
    employee_id = get_employee_id(auth.uid()) OR -- Own reviews
    reviewer_id = get_employee_id(auth.uid()) OR -- Reviews they conduct
    (get_user_role_level(auth.uid()) >= 2 AND employee_id IN (
        SELECT e.id FROM employees e
        WHERE e.team_id IN (
            SELECT team_id FROM employees WHERE user_id = auth.uid()
        )
    )) OR -- Admin sees team reviews
    get_user_role_level(auth.uid()) >= 3 -- Superadmin sees all
);

-- Admins+ can create performance reviews
CREATE POLICY "performance_reviews_insert_policy" ON performance_reviews FOR INSERT
WITH CHECK (get_user_role_level(auth.uid()) >= 2);

-- Reviewers and Admins+ can update reviews
CREATE POLICY "performance_reviews_update_policy" ON performance_reviews FOR UPDATE
USING (
    reviewer_id = get_employee_id(auth.uid()) OR
    get_user_role_level(auth.uid()) >= 2
)
WITH CHECK (
    reviewer_id = get_employee_id(auth.uid()) OR
    get_user_role_level(auth.uid()) >= 2
);

-- Only Superadmins can delete performance reviews
CREATE POLICY "performance_reviews_delete_policy" ON performance_reviews FOR DELETE
USING (get_user_role_level(auth.uid()) >= 3);

-- =====================================================
-- PERFORMANCE GOALS POLICIES
-- =====================================================

-- Users can view their own goals, Admins+ can view team goals
CREATE POLICY "performance_goals_select_policy" ON performance_goals FOR SELECT
USING (
    employee_id = get_employee_id(auth.uid()) OR -- Own goals
    (get_user_role_level(auth.uid()) >= 2 AND employee_id IN (
        SELECT e.id FROM employees e
        WHERE e.team_id IN (
            SELECT team_id FROM employees WHERE user_id = auth.uid()
        )
    )) OR -- Admin sees team goals
    get_user_role_level(auth.uid()) >= 3 -- Superadmin sees all
);

-- Users can create their own goals, Admins+ can create for team
CREATE POLICY "performance_goals_insert_policy" ON performance_goals FOR INSERT
WITH CHECK (
    employee_id = get_employee_id(auth.uid()) OR
    get_user_role_level(auth.uid()) >= 2
);

-- Users can update their own goals, Admins+ can update team goals
CREATE POLICY "performance_goals_update_policy" ON performance_goals FOR UPDATE
USING (
    employee_id = get_employee_id(auth.uid()) OR
    get_user_role_level(auth.uid()) >= 2
)
WITH CHECK (
    employee_id = get_employee_id(auth.uid()) OR
    get_user_role_level(auth.uid()) >= 2
);

-- Only Admins+ can delete goals
CREATE POLICY "performance_goals_delete_policy" ON performance_goals FOR DELETE
USING (get_user_role_level(auth.uid()) >= 2);

-- =====================================================
-- DOCUMENT MANAGEMENT POLICIES
-- =====================================================

-- USER ROLE: View own documents
-- ADMIN ROLE: View team documents (non-confidential)
-- SUPERADMIN ROLE: View all documents
CREATE POLICY "employee_documents_select_policy" ON employee_documents FOR SELECT
USING (
    employee_id = get_employee_id(auth.uid()) OR -- Own documents
    (get_user_role_level(auth.uid()) >= 2 AND
     access_level IN ('employee', 'manager') AND
     NOT is_confidential) OR -- Admin sees non-confidential team docs
    get_user_role_level(auth.uid()) >= 3 -- Superadmin sees all
);

-- Users can upload their own documents, Admins+ can upload for team
CREATE POLICY "employee_documents_insert_policy" ON employee_documents FOR INSERT
WITH CHECK (
    employee_id = get_employee_id(auth.uid()) OR
    get_user_role_level(auth.uid()) >= 2
);

-- Users can update their own documents, Admins+ can update team documents
CREATE POLICY "employee_documents_update_policy" ON employee_documents FOR UPDATE
USING (
    employee_id = get_employee_id(auth.uid()) OR
    get_user_role_level(auth.uid()) >= 2
)
WITH CHECK (
    employee_id = get_employee_id(auth.uid()) OR
    get_user_role_level(auth.uid()) >= 2
);

-- Only Admins+ can delete documents
CREATE POLICY "employee_documents_delete_policy" ON employee_documents FOR DELETE
USING (get_user_role_level(auth.uid()) >= 2);

-- =====================================================
-- NOTIFICATIONS POLICIES
-- =====================================================

-- Users can only see their own notifications
CREATE POLICY "notifications_select_policy" ON notifications FOR SELECT
USING (recipient_id = auth.uid());

-- System can create notifications (handled by triggers/functions)
CREATE POLICY "notifications_insert_policy" ON notifications FOR INSERT
WITH CHECK (true);

-- Users can mark their own notifications as read
CREATE POLICY "notifications_update_policy" ON notifications FOR UPDATE
USING (recipient_id = auth.uid())
WITH CHECK (recipient_id = auth.uid());

-- Users can delete their own notifications
CREATE POLICY "notifications_delete_policy" ON notifications FOR DELETE
USING (recipient_id = auth.uid());

-- =====================================================
-- SYSTEM CONFIGURATION POLICIES
-- =====================================================

-- Everyone can view public holidays
CREATE POLICY "holidays_select_policy" ON holidays FOR SELECT
USING (true);

-- Only Superadmins can manage holidays
CREATE POLICY "holidays_insert_policy" ON holidays FOR INSERT
WITH CHECK (get_user_role_level(auth.uid()) >= 3);

CREATE POLICY "holidays_update_policy" ON holidays FOR UPDATE
USING (get_user_role_level(auth.uid()) >= 3)
WITH CHECK (get_user_role_level(auth.uid()) >= 3);

CREATE POLICY "holidays_delete_policy" ON holidays FOR DELETE
USING (get_user_role_level(auth.uid()) >= 3);

-- =====================================================
-- SYSTEM SETTINGS POLICIES
-- =====================================================

-- Users can view public settings, Superadmins can view all
CREATE POLICY "system_settings_select_policy" ON system_settings FOR SELECT
USING (
    is_public = true OR
    get_user_role_level(auth.uid()) >= 3
);

-- Only Superadmins can manage system settings
CREATE POLICY "system_settings_insert_policy" ON system_settings FOR INSERT
WITH CHECK (get_user_role_level(auth.uid()) >= 3);

CREATE POLICY "system_settings_update_policy" ON system_settings FOR UPDATE
USING (get_user_role_level(auth.uid()) >= 3)
WITH CHECK (get_user_role_level(auth.uid()) >= 3);

CREATE POLICY "system_settings_delete_policy" ON system_settings FOR DELETE
USING (get_user_role_level(auth.uid()) >= 3);

-- =====================================================
-- ROLE ASSIGNMENT POLICIES
-- =====================================================

-- Users can view their own role assignments, Superadmins can view all
CREATE POLICY "user_role_assignments_select_policy" ON user_role_assignments FOR SELECT
USING (
    user_id = auth.uid() OR
    get_user_role_level(auth.uid()) >= 3
);

-- Only Superadmins can assign/modify user roles
CREATE POLICY "user_role_assignments_insert_policy" ON user_role_assignments FOR INSERT
WITH CHECK (get_user_role_level(auth.uid()) >= 3);

CREATE POLICY "user_role_assignments_update_policy" ON user_role_assignments FOR UPDATE
USING (get_user_role_level(auth.uid()) >= 3)
WITH CHECK (get_user_role_level(auth.uid()) >= 3);

CREATE POLICY "user_role_assignments_delete_policy" ON user_role_assignments FOR DELETE
USING (get_user_role_level(auth.uid()) >= 3);

-- =====================================================
-- SYSTEM ROLES POLICIES
-- =====================================================

-- Everyone can view system roles (for UI purposes)
CREATE POLICY "system_roles_select_policy" ON system_roles FOR SELECT
USING (true);

-- Only Superadmins can manage system roles
CREATE POLICY "system_roles_insert_policy" ON system_roles FOR INSERT
WITH CHECK (get_user_role_level(auth.uid()) >= 3);

CREATE POLICY "system_roles_update_policy" ON system_roles FOR UPDATE
USING (get_user_role_level(auth.uid()) >= 3)
WITH CHECK (get_user_role_level(auth.uid()) >= 3);

CREATE POLICY "system_roles_delete_policy" ON system_roles FOR DELETE
USING (get_user_role_level(auth.uid()) >= 3);

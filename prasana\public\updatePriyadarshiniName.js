// Script to update <PERSON><PERSON><PERSON><PERSON><PERSON>'s name in the database
// Remove "M2" from last_name field

async function updatePriyadarshiniName() {
  console.log('🔄 Starting Priyadarshini name update...');
  
  try {
    // This assumes supabase client is available globally
    const supabase = window.supabase || (await import('/src/supabaseClient.js')).supabase;
    
    if (!supabase) {
      throw new Error('Supabase client not found');
    }

    // Step 1: Find Priyadarshini's record
    const { data: priyadarshiniData, error: findError } = await supabase
      .from('employees')
      .select('id, first_name, last_name, designation')
      .ilike('first_name', '%Priyadarshini%')
      .single();

    if (findError) {
      console.log('⚠️ Priyadarshini not found in database, this is expected if not created yet');
      return {
        success: true,
        message: 'No existing Priyadarshini record to update'
      };
    }

    console.log('✅ Found Priyadarshini:', priyadarshiniData);

    // Step 2: Update her name to remove M2
    const { data: updateData, error: updateError } = await supabase
      .from('employees')
      .update({
        last_name: '', // Remove M2, just use first name
        updated_at: new Date().toISOString()
      })
      .eq('id', priyadarshiniData.id)
      .select();

    if (updateError) {
      throw new Error('Failed to update Priyadarshini name: ' + updateError.message);
    }

    console.log('✅ Updated Priyadarshini name successfully:', updateData);

    // Step 3: Verify the update
    const { data: verifyData, error: verifyError } = await supabase
      .from('employees')
      .select('id, first_name, last_name, designation')
      .eq('id', priyadarshiniData.id)
      .single();

    if (verifyError) {
      throw new Error('Failed to verify update: ' + verifyError.message);
    }

    console.log('✅ Verification - Updated record:', verifyData);

    return {
      success: true,
      message: 'Priyadarshini name updated successfully',
      before: priyadarshiniData,
      after: verifyData
    };

  } catch (error) {
    console.error('❌ Error updating Priyadarshini name:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Auto-run if in browser console
if (typeof window !== 'undefined') {
  console.log('🚀 Priyadarshini Name Update Script Loaded');
  console.log('Run updatePriyadarshiniName() to execute the update');
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { updatePriyadarshiniName };
}

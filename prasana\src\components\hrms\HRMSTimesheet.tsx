import React, { useState, useEffect } from 'react';
import { 
  Clock, 
  Plus, 
  Calendar, 
  Edit2, 
  Trash2, 
  Save, 
  X,
  ChevronLeft,
  ChevronRight,
  Filter,
  Download
} from 'lucide-react';
import { useHRMS } from '../../contexts/HRMSContext';
import { timesheetService } from '../../services/timesheetService';
import { TimeEntry, TimeEntryForm } from '../../types/timesheet';

interface TimesheetEntry {
  id?: string;
  date: string;
  project: string;
  description: string;
  hours: number;
  minutes: number;
  category?: string;
}

const HRMSTimesheet: React.FC = () => {
  const { currentEmployee } = useHRMS();
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingEntry, setEditingEntry] = useState<TimeEntry | null>(null);
  const [currentWeek, setCurrentWeek] = useState(new Date());
  const [formData, setFormData] = useState<TimesheetEntry>({
    date: new Date().toISOString().split('T')[0],
    project: '',
    description: '',
    hours: 0,
    minutes: 0,
    category: 'Development'
  });

  const categories = [
    'Development',
    'Design', 
    'Planning',
    'Meeting',
    'Research',
    'Documentation',
    'Testing',
    'Other'
  ];

  useEffect(() => {
    if (currentEmployee) {
      loadTimeEntries();
    }
  }, [currentEmployee, currentWeek]);

  const getWeekRange = () => {
    const start = new Date(currentWeek);
    start.setDate(start.getDate() - start.getDay()); // Start of week (Sunday)
    const end = new Date(start);
    end.setDate(start.getDate() + 6); // End of week (Saturday)
    return { start, end };
  };

  const loadTimeEntries = async () => {
    try {
      setLoading(true);
      const { start, end } = getWeekRange();
      const entries = await timesheetService.getTimeEntries(
        start.toISOString().split('T')[0],
        end.toISOString().split('T')[0]
      );
      setTimeEntries(entries);
    } catch (error) {
      console.error('Error loading time entries:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const entryData: TimeEntryForm = {
        projectId: formData.project,
        description: formData.description,
        date: formData.date,
        hours: formData.hours,
        minutes: formData.minutes,
        category: formData.category
      };

      if (editingEntry) {
        await timesheetService.updateTimeEntry(editingEntry.id, entryData);
      } else {
        await timesheetService.addTimeEntry(entryData);
      }

      // Reset form and reload entries
      setFormData({
        date: new Date().toISOString().split('T')[0],
        project: '',
        description: '',
        hours: 0,
        minutes: 0,
        category: 'Development'
      });
      setShowAddForm(false);
      setEditingEntry(null);
      await loadTimeEntries();
    } catch (error) {
      console.error('Error saving time entry:', error);
    }
  };

  const handleEdit = (entry: TimeEntry) => {
    setEditingEntry(entry);
    setFormData({
      id: entry.id,
      date: entry.date,
      project: entry.project_name_text || '',
      description: entry.description,
      hours: Math.floor(entry.duration / 60),
      minutes: entry.duration % 60,
      category: entry.category || 'Development'
    });
    setShowAddForm(true);
  };

  const handleDelete = async (entryId: string) => {
    if (!window.confirm('Are you sure you want to delete this time entry?')) return;
    
    try {
      await timesheetService.deleteTimeEntry(entryId);
      await loadTimeEntries();
    } catch (error) {
      console.error('Error deleting time entry:', error);
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const navigateWeek = (direction: 'prev' | 'next') => {
    const newWeek = new Date(currentWeek);
    newWeek.setDate(newWeek.getDate() + (direction === 'next' ? 7 : -7));
    setCurrentWeek(newWeek);
  };

  const getWeekDays = () => {
    const { start } = getWeekRange();
    const days = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(start);
      day.setDate(start.getDate() + i);
      days.push(day);
    }
    return days;
  };

  const getEntriesForDate = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    return timeEntries.filter(entry => entry.date === dateStr);
  };

  const getTotalHoursForWeek = () => {
    return timeEntries.reduce((total, entry) => total + entry.duration, 0);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold mb-2">Timesheet Management</h1>
            <p className="text-blue-100">Track your work hours and projects</p>
          </div>
          <button
            onClick={() => setShowAddForm(true)}
            className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all"
          >
            <Plus className="w-4 h-4" />
            <span>Add Entry</span>
          </button>
        </div>
      </div>

      {/* Week Navigation */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigateWeek('prev')}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
            <h2 className="text-lg font-semibold">
              {getWeekRange().start.toLocaleDateString('en-US', { month: 'long', day: 'numeric' })} - {getWeekRange().end.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}
            </h2>
            <button
              onClick={() => navigateWeek('next')}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm text-gray-600">Total Hours</p>
              <p className="text-xl font-bold text-blue-600">{formatDuration(getTotalHoursForWeek())}</p>
            </div>
          </div>
        </div>

        {/* Week Grid */}
        <div className="grid grid-cols-7 gap-4">
          {getWeekDays().map((day, index) => {
            const dayEntries = getEntriesForDate(day);
            const dayTotal = dayEntries.reduce((sum, entry) => sum + entry.duration, 0);
            const isToday = day.toDateString() === new Date().toDateString();

            return (
              <div
                key={index}
                className={`border rounded-lg p-3 min-h-[120px] ${
                  isToday ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                }`}
              >
                <div className="text-center mb-2">
                  <p className="text-sm font-medium text-gray-900">
                    {day.toLocaleDateString('en-US', { weekday: 'short' })}
                  </p>
                  <p className="text-lg font-bold text-gray-700">
                    {day.getDate()}
                  </p>
                  {dayTotal > 0 && (
                    <p className="text-xs text-blue-600 font-medium">
                      {formatDuration(dayTotal)}
                    </p>
                  )}
                </div>
                <div className="space-y-1">
                  {dayEntries.map((entry) => (
                    <div
                      key={entry.id}
                      className="bg-white p-2 rounded border text-xs cursor-pointer hover:shadow-sm"
                      onClick={() => handleEdit(entry)}
                    >
                      <p className="font-medium truncate">{entry.project_name_text}</p>
                      <p className="text-gray-600 truncate">{entry.description}</p>
                      <p className="text-blue-600 font-medium">{formatDuration(entry.duration)}</p>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Time Entries List */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Recent Entries</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {timeEntries.slice(0, 10).map((entry) => (
            <div key={entry.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <Clock className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{entry.project_name_text}</h4>
                    <p className="text-sm text-gray-600">{entry.description}</p>
                    <p className="text-xs text-gray-500">
                      {new Date(entry.date).toLocaleDateString()} • {entry.category}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="text-lg font-bold text-blue-600">
                    {formatDuration(entry.duration)}
                  </span>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleEdit(entry)}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      <Edit2 className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(entry.id)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
          {timeEntries.length === 0 && (
            <div className="p-8 text-center text-gray-500">
              No time entries found for this week
            </div>
          )}
        </div>
      </div>

      {/* Add/Edit Form Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">
                  {editingEntry ? 'Edit Time Entry' : 'Add Time Entry'}
                </h3>
                <button
                  onClick={() => {
                    setShowAddForm(false);
                    setEditingEntry(null);
                    setFormData({
                      date: new Date().toISOString().split('T')[0],
                      project: '',
                      description: '',
                      hours: 0,
                      minutes: 0,
                      category: 'Development'
                    });
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>
            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                <input
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Project</label>
                <input
                  type="text"
                  value={formData.project}
                  onChange={(e) => setFormData({ ...formData, project: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter project name"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Describe what you worked on"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Hours</label>
                  <input
                    type="number"
                    min="0"
                    max="24"
                    value={formData.hours}
                    onChange={(e) => setFormData({ ...formData, hours: parseInt(e.target.value) || 0 })}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Minutes</label>
                  <select
                    value={formData.minutes}
                    onChange={(e) => setFormData({ ...formData, minutes: parseInt(e.target.value) })}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {[0, 15, 30, 45].map(min => (
                      <option key={min} value={min}>{min}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select
                  value={formData.category}
                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg flex items-center justify-center space-x-2"
                >
                  <Save className="w-4 h-4" />
                  <span>{editingEntry ? 'Update' : 'Save'} Entry</span>
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowAddForm(false);
                    setEditingEntry(null);
                  }}
                  className="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default HRMSTimesheet;

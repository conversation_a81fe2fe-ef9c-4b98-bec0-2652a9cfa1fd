import React, { useEffect, useState } from 'react';
import ProfileAvatar from './ProfileAvatar';
import { TeamMember } from '../types/team';
import { timesheetService } from '../services/timesheetService';
import { TimeEntry } from '../types/timesheet';
import { useUser } from '../contexts/UserContext';
import { supabase } from '../supabaseClient';
import { fetchTeamMembers } from '../data/supabaseTeams';
import '../styles/EmployeeManagement.css';

// Helper to get Monday of the current week
function getMonday(d: Date) {
  d = new Date(d);
  const day = d.getDay();
  const diff = d.getDate() - day + (day === 0 ? -6 : 1); // adjust when day is Sunday
  return new Date(d.setDate(diff));
}
// Helper to get Sunday of the current week
function getSunday(d: Date) {
  d = new Date(d);
  const day = d.getDay();
  const diff = d.getDate() - day + 7;
  return new Date(d.setDate(diff));
}

// Fetch all team members from database (no duplicates, correct teams)
const getAllTeamMembersFromDB = async (): Promise<TeamMember[]> => {
  try {
    const members = await fetchTeamMembers();
    // Filter out inactive members, CEO, and ensure no duplicates
    const activeMembers = members.filter(member =>
      member.status === 'active' &&
      member.uuid && // Only active members with valid UUIDs
      member.designation !== 'CEO' && // Hide CEO from main dashboard
      member.designation !== 'Chief Executive Officer' && // Alternative CEO title
      member.name !== 'ARUN G' // Explicit name filter as backup
    );

    // Remove duplicates based on UUID
    const uniqueMembers = activeMembers.filter((member, index, self) =>
      index === self.findIndex(m => m.uuid === member.uuid)
    );

    return uniqueMembers;
  } catch (error) {
    console.error('Error fetching team members from database:', error);
    return [];
  }
};

const EmployeeManagement: React.FC = () => {
  const [memberHours, setMemberHours] = useState<{ [uuid: string]: number }>({});
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [weekRange, setWeekRange] = useState<{ start: string; end: string }>({ start: '', end: '' });
  const [teamFilter, setTeamFilter] = useState<string>('');
  const [nameFilter, setNameFilter] = useState<string>('');
  const { currentUser } = useUser();

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch team members from database (no duplicates, correct teams)
        const allMembers = await getAllTeamMembersFromDB();
        setTeamMembers(allMembers);

        // Get current week's Monday and Sunday
        const now = new Date();
        const monday = getMonday(now);
        const sunday = getSunday(now);
        const startDate = monday.toISOString().slice(0, 10);
        const endDate = sunday.toISOString().slice(0, 10);
        setWeekRange({ start: startDate, end: endDate });

        // Fetch ALL time entries for this week (all users)
        const entries: TimeEntry[] = await timesheetService.getAllTimeEntries(startDate, endDate);

        // Aggregate hours for each member by user_id (UUID)
        const hours: { [uuid: string]: number } = {};
        entries.forEach(entry => {
          // Use user_id as the primary key for aggregation
          if (entry.user_id) {
            hours[entry.user_id] = (hours[entry.user_id] || 0) + entry.duration;
          }
        });

        console.log('Employee Management - Fetched members:', allMembers.length);
        console.log('Employee Management - Date range:', { startDate, endDate });
        console.log('Employee Management - Time entries:', entries.length);

        setMemberHours(hours);
      } catch (error) {
        console.error('Error fetching employee management data:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  // Update the filteredMembers logic to include the new filters
  const filteredMembers = teamMembers.filter(member => {
    const matchesTeam = !teamFilter || member.team === teamFilter;
    const matchesName = !nameFilter || member.name.toLowerCase().includes(nameFilter.toLowerCase());
    return matchesTeam && matchesName;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Professional Header */}
        <div className="header-section bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
          <div className="px-6 py-8 border-b border-gray-200">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="mb-4 lg:mb-0">
                <h1 className="text-2xl font-semibold text-gray-900 mb-1">
                  Employee Management
                </h1>
                <p className="text-sm text-gray-600">
                  Monitor team performance and track weekly timesheet data
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                <div className="bg-blue-50 border border-blue-200 rounded-lg px-4 py-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs font-medium text-blue-600 uppercase tracking-wide">Current Period</p>
                      <p className="text-sm font-semibold text-blue-900 mt-1">
                        {weekRange.start} - {weekRange.end}
                      </p>
                    </div>
                    <div className="ml-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 border border-green-200 rounded-lg px-4 py-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs font-medium text-green-600 uppercase tracking-wide">Total Hours</p>
                      <p className="text-sm font-semibold text-green-900 mt-1">
                        {(Object.values(memberHours).reduce((sum, hours) => sum + hours, 0) / 60).toFixed(1)}h
                      </p>
                    </div>
                    <div className="ml-3">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Status Bar */}
          <div className="px-6 py-4 bg-gray-50">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-gray-600">
                    {filteredMembers.filter(m => {
                      const key = m.uuid || m.name;
                      const hours = memberHours[key] || memberHours[m.uuid] || memberHours[m.name] || 0;
                      return hours > 0;
                    }).length} Active
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                  <span className="text-gray-600">
                    {filteredMembers.filter(m => {
                      const key = m.uuid || m.name;
                      const hours = memberHours[key] || memberHours[m.uuid] || memberHours[m.name] || 0;
                      return hours === 0;
                    }).length} Inactive
                  </span>
                </div>
              </div>
              <div className="text-gray-500">
                Last updated: {new Date().toLocaleTimeString()}
              </div>
            </div>
          </div>
        </div>
        {/* Professional Filters */}
        <div className="filter-section bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Filters</h3>
          </div>

          {loading ? (
            <div className="px-6 py-8">
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            </div>
          ) : (
            <div className="px-6 py-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="team-filter" className="block text-sm font-medium text-gray-700 mb-2">
                    Team
                  </label>
                  <select
                    id="team-filter"
                    className="focus-ring block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white"
                    value={teamFilter}
                    onChange={(e) => setTeamFilter(e.target.value)}
                  >
                    <option value="">All Teams</option>
                    {Array.from(new Set(teamMembers.map(m => m.team))).map(team => (
                      <option key={team} value={team}>{team}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="name-filter" className="block text-sm font-medium text-gray-700 mb-2">
                    Search
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </div>
                    <input
                      id="name-filter"
                      type="text"
                      className="focus-ring block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      placeholder="Search employees..."
                      value={nameFilter}
                      onChange={(e) => setNameFilter(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              {(teamFilter || nameFilter) && (
                <div className="mt-4 flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    Showing {filteredMembers.length} of {teamMembers.length} employees
                  </div>
                  <button
                    onClick={() => {
                      setTeamFilter('');
                      setNameFilter('');
                    }}
                    className="text-sm text-blue-600 hover:text-blue-500 font-medium"
                  >
                    Clear filters
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
        {/* Employee Grid */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">
                Team Members ({filteredMembers.length})
              </h3>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <span>View:</span>
                <button className="text-blue-600 font-medium">Grid</button>
                <span>|</span>
                <button className="text-gray-400">List</button>
              </div>
            </div>
          </div>

          {loading ? (
            <div className="px-6 py-12">
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            </div>
          ) : (
            <div className="p-6">
              <div className="employee-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredMembers.map((member, index) => {
                  const key = member.uuid || member.name;
                  const memberHoursValue = memberHours[key] || memberHours[member.uuid] || memberHours[member.name] || 0;
                  const hours = (memberHoursValue / 60).toFixed(1);
                  const isTopPerformer = memberHoursValue > 0 && memberHoursValue === Math.max(...Object.values(memberHours));

                  return (
                    <div
                      key={member.uuid || `${member.name}-${member.team}-${index}`}
                      className="employee-card relative bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow duration-200"
                    >
                      {/* Top Performer Badge */}
                      {isTopPerformer && memberHoursValue > 0 && (
                        <div className="absolute top-3 right-3">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            ⭐ Top Performer
                          </span>
                        </div>
                      )}

                      <div className="flex flex-col items-center text-center">
                        {/* Profile Avatar */}
                        <div className="relative mb-4">
                          <ProfileAvatar name={member.name} avatar={member.avatar} size="lg" />

                          {/* Status Indicator */}
                          <div className={`absolute bottom-0 right-0 w-4 h-4 rounded-full border-2 border-white ${
                            memberHoursValue > 0 ? 'bg-green-400' : 'bg-gray-300'
                          }`}></div>

                          {/* Team Logo */}
                          {member.team && (
                            <div className="absolute -bottom-1 -left-1 bg-white rounded-full p-1 shadow-sm border border-gray-200">
                              <img
                                src={`/profiles/${member.team.toLowerCase()}.png`}
                                alt={`${member.team} Logo`}
                                className="w-5 h-5 object-contain"
                              />
                            </div>
                          )}
                        </div>

                        {/* Member Info */}
                        <div className="mb-4">
                          <h4 className="text-sm font-semibold text-gray-900 mb-1">
                            {member.name}
                          </h4>
                          <p className="text-xs text-gray-500 mb-2">
                            {member.role}
                          </p>
                          {member.team && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                              {member.team}
                            </span>
                          )}
                        </div>

                        {/* Hours Display */}
                        <div className="w-full">
                          <div className={`inline-flex items-center justify-center w-full py-2 px-3 rounded-md text-sm font-medium ${
                            memberHoursValue > 0
                              ? 'bg-green-50 text-green-700 border border-green-200'
                              : 'bg-gray-50 text-gray-500 border border-gray-200'
                          }`}>
                            <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {hours} hours
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {filteredMembers.length === 0 && (
                <div className="text-center py-12">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No employees found</h3>
                  <p className="mt-1 text-sm text-gray-500">Try adjusting your search or filter criteria.</p>
                </div>
              )}
            </div>
          )}
        </div>
        {/* Analytics Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Performance Analytics</h3>
                <p className="text-sm text-gray-500 mt-1">
                  Weekly overview for {weekRange.start} - {weekRange.end}
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <p className="text-sm text-gray-500">Average Hours</p>
                  <p className="text-lg font-semibold text-gray-900">
                    {filteredMembers.length > 0
                      ? ((Object.values(memberHours).reduce((sum, hours) => sum + hours, 0) / 60) / filteredMembers.length).toFixed(1)
                      : '0.0'
                    }h
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500">Total Hours</p>
                  <p className="text-lg font-semibold text-gray-900">
                    {(Object.values(memberHours).reduce((sum, hours) => sum + hours, 0) / 60).toFixed(1)}h
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="p-6">
            {/* Chart */}
            <div className="bg-gray-50 rounded-lg p-6">
              <div className="h-64 flex items-end justify-center space-x-2 border-b border-l border-gray-300 relative">
                {/* Y-axis labels */}
                <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-500 -ml-12">
                  {[...Array(5)].map((_, i) => {
                    const maxHours = Math.max(...filteredMembers.map(m => {
                      const key = m.uuid || m.name;
                      return (memberHours[key] || memberHours[m.uuid] || memberHours[m.name] || 0) / 60;
                    }), 1);
                    const value = ((4 - i) * maxHours / 4).toFixed(1);
                    return (
                      <span key={i} className="font-mono text-xs">
                        {value}h
                      </span>
                    );
                  })}
                </div>

                {filteredMembers.length > 0 ? filteredMembers.map((member, index) => {
                  const key = member.uuid || member.name;
                  const memberHoursValue = memberHours[key] || memberHours[member.uuid] || memberHours[member.name] || 0;
                  const hours = memberHoursValue / 60;
                  const maxHours = Math.max(...filteredMembers.map(m => {
                    const k = m.uuid || m.name;
                    return (memberHours[k] || memberHours[m.uuid] || memberHours[m.name] || 0) / 60;
                  }), 1);
                  const barHeight = (hours / maxHours) * 100;
                  const isTopPerformer = memberHoursValue > 0 && memberHoursValue === Math.max(...Object.values(memberHours));

                  return (
                    <div
                      key={member.uuid || `${member.name}-${member.team}-chart-${index}`}
                      className="chart-bar flex flex-col items-center group"
                    >
                      {/* Bar */}
                      <div
                        className={`w-8 sm:w-12 rounded-t-md transition-all duration-300 hover:opacity-80 ${
                          isTopPerformer && memberHoursValue > 0
                            ? 'bg-yellow-400'
                            : hours > 0
                              ? 'bg-blue-500'
                              : 'bg-gray-300'
                        }`}
                        style={{
                          height: `${Math.max(barHeight, 4)}%`,
                          minHeight: '4px'
                        }}
                        title={`${member.name}: ${hours.toFixed(1)} hours`}
                      >
                      </div>

                      {/* Member info */}
                      <div className="mt-2 flex flex-col items-center">
                        <div className="w-6 h-6 rounded-full overflow-hidden border border-gray-200 mb-1">
                          <ProfileAvatar name={member.name} avatar={member.avatar} size="sm" />
                        </div>
                        <div className="text-xs text-gray-600 text-center max-w-12 truncate" title={member.name}>
                          {member.name.split(' ')[0]}
                        </div>
                        <div className="text-xs font-medium text-gray-900 mt-1">
                          {hours.toFixed(1)}h
                        </div>
                      </div>
                    </div>
                  );
                }) : (
                  <div className="flex items-center justify-center h-full w-full text-gray-500">
                    <div className="text-center">
                      <svg className="mx-auto h-12 w-12 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                      <p className="text-sm">No data to display</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Legend */}
              {filteredMembers.length > 0 && (
                <div className="mt-6 flex flex-wrap items-center justify-between gap-4">
                  <div className="flex items-center space-x-6">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-yellow-400 rounded"></div>
                      <span className="text-sm text-gray-600">Top Performer</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-blue-500 rounded"></div>
                      <span className="text-sm text-gray-600">Active</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-gray-300 rounded"></div>
                      <span className="text-sm text-gray-600">Inactive</span>
                    </div>
                  </div>

                  <div className="text-sm text-gray-600">
                    Top Performer: {(() => {
                      const topUser = Object.entries(memberHours).reduce((a, b) => (a[1] > b[1] ? a : b), ['', 0]);
                      const topMember = filteredMembers.find(m => (m.uuid || m.name) === topUser[0]);
                      return topMember ? `${topMember.name} (${(topUser[1] / 60).toFixed(1)}h)` : 'None';
                    })()}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeManagement; 
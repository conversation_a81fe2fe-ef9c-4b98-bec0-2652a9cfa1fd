import { Teams } from '../types/team';

// Helper function to get local profile image path - REMOVED
// const getProfileImage = (name: string) => {
//     // Convert name to lowercase and replace spaces with underscores
//     const imageName = name.toLowerCase().replace(/\s+/g, '_');
//     return `/profiles/${imageName}.jpg`;  // Assuming images are in JPG format
// };

export const teams: Teams = {
    athena: {
        name: 'ATH<PERSON><PERSON>',
        sdm: {
            name: '<PERSON> <PERSON>',
            role: 'Service Delivery Manager',
            designation: 'Service Delivery Manager',
            avatar: '/profiles/sri_ram.png',
            uuid: 'fe626847-a16d-4209-8924-ffac949dd123',
        },
        tdm: {
            name: '<PERSON><PERSON><PERSON><PERSON>',
            role: 'Technical Delivery Manager',
            designation: 'Technical Delivery Manager',
            avatar: '/profiles/selvandrane.png',
            uuid: 'dfb73b01-2ff7-4f5e-a263-d148f23eb275',
        },
        cxm: {
            name: '<PERSON><PERSON><PERSON><PERSON>',
            role: 'Client Experience Manager',
            designation: 'Client Experience Manager',
            avatar: '/profiles/mahesh.png',
            uuid: 'afe56739-136c-4b23-aa6c-f304c35fbc4d',
        },
        members: [
            {
                name: 'Shri Mathi',
                role: 'Associate Trainee',
                avatar: '/profiles/shri.png',
                uuid: 'fabaddf9-6717-432c-8223-a980a180ad4e',
            },
            {
                name: 'Prasanna',
                role: 'Associate Trainee',
                avatar: '/profiles/prasana.png',
                uuid: '933560d2-4fe7-47b4-a8ba-4ace3f0f9834',
            },
            {
                name: 'Kavin Mithra V',
                role: 'Associate Trainee',
                avatar: '/profiles/kavinmithra.jpg',
                uuid: 'f402ab2a-ea3a-4196-8b39-6426e6336e34',
            },
            {
                name: 'Fazeela',
                role: 'Associate Trainee',
                avatar: '/profiles/fazzela.png',
                uuid: '1ac09f00-eda5-43e7-a093-d80a427ae7ab',
            },
            {
                name: 'Sivaranjani',
                role: 'Associate Trainee',
                avatar: '/profiles/sivaranjani.png',
                uuid: '04e27ae3-11f7-46a3-aa96-e52d35c05e27',
            }
        ]
    },
    dynamix: {
        name: 'DYNAMIX',
        sdm: {
            name: 'Yuvaraj',
            role: 'Service Delivery Manager',
            designation: 'Service Delivery Manager',
            avatar: '/profiles/yuvaraj.png',
            uuid: 'f7de5b53-411e-4801-9263-4d2c97a659da',
        },
        cxm: {
            name: 'Purushoth',
            role: 'Client Experience Manager',
            designation: 'Client Experience Manager',
            avatar: '/profiles/pourushoth.png',
            uuid: 'cae078a3-10e8-4704-8c9f-587ea1b936fc',
        },
        tdm: {
            name: 'Kiyshore K',
            role: 'Technical Delivery Manager',
            designation: 'Technical Delivery Manager',
            avatar: '/profiles/kiyshor.png',
            uuid: 'd25f5828-24b9-42ac-8373-839883e7a55e',
        },
        members: [
            {
                name: 'Nithish K',
                role: 'Associate Trainee',
                avatar: '/profiles/Knithish.jpg',
                uuid: '163986c9-e238-4308-aa7e-bb1a78eb0332',
            },
            {
                name: 'Nitesh S',
                role: 'Associate Trainee',
                avatar: '/profiles/niteshS.jpg',
                uuid: 'e3d9d4d1-8306-4732-90e7-27d373d944b6',
            },
            {
                name: 'S. Keerthipriya',
                role: 'Associate Trainee',
                avatar: '/profiles/keerthipriya.jpg',
                uuid: '3b740158-0a9c-4cab-ac46-6d774b62e8fb',
            }
        ]
    },
    nexus: {
        name: 'NEXUS',
        sdm: {
            name: 'Eashwara Prasadh',
            role: 'Service Delivery Manager',
            designation: 'Service Delivery Manager',
            avatar: '/profiles/eashwara_prasadh.jpg',
            uuid: '6f1340f2-b794-425d-91f3-e90acb887788',
        },
        cxm: {
            name: 'Darshan K',
            role: 'Client Experience Manager',
            designation: 'Client Experience Manager',
            avatar: '/profiles/darshan.png',
            uuid: 'e504e8e4-d097-4e03-8043-38fe4cf01870',
        },
        tdm: {
            name: 'Yusuf Fayas',
            role: 'Technical Delivery Manager',
            designation: 'Technical Delivery Manager',
            avatar: '/profiles/yusuf.png',
            uuid: '929218c1-e19f-4d75-b38f-a8938ef4308e',
        },
        members: [
            {
                name: 'Gaushik Adhiban E',
                role: 'Associate Trainee',
                avatar: '/profiles/gaushik.png',
                uuid: '9056f342-c554-4276-8f6b-14c658d4b73e',
            },
            {
                name: 'Hariharan B',
                role: 'Associate Trainee',
                avatar: '/profiles/hariharan.jpg',
                uuid: 'afa0c376-9452-4e13-9fa3-9a3d3b7a6899',
            },
            {
                name: 'Sakthivel N',
                role: 'Associate Trainee',
                avatar: '/profiles/Sackthivel.jpg',
                uuid: '66ef0b84-44c9-4121-8c82-097cf063ae88',
            }
        ]
    },
    titan: {
        name: 'TITAN',
        sdm: {
            name: 'Aamina Begam T',
            role: 'Service Delivery Manager',
            designation: 'Service Delivery Manager',
            avatar: '/profiles/aamina.png',
            uuid: '76a9fa8f-0ad8-49ca-ab76-fba78c1eb4d8',
        },
        cxm: {
            name: 'Yamini',
            role: 'Client Experience Manager',
            designation: 'Client Experience Manager',
            avatar: '/profiles/yamini.png',
            uuid: '831d30d0-d231-4a46-9918-67d425678e21',
        },
        tdm: {
            name: 'Sivaranjani',
            role: 'Technical Delivery Manager',
            designation: 'Technical Delivery Manager',
            avatar: '/profiles/sivaranjani.png',
            uuid: '04e27ae3-11f7-46a3-aa96-e52d35c05e27',
        },
        members: [
            {
                name: 'Fazeela',
                role: 'Associate Trainee',
                avatar: '/profiles/fazzela.png',
                uuid: '1ac09f00-eda5-43e7-a093-d80a427ae7ab',
            },
            {
                name: 'Kavin Mithra V',
                role: 'Associate Trainee',
                avatar: '/profiles/kavinmithra.jpg',
                uuid: 'f402ab2a-ea3a-4196-8b39-6426e6336e34',
            },
            {
                name: 'S. Keerthipriya',
                role: 'Associate Trainee',
                avatar: '/profiles/keerthipriya.jpg',
                uuid: '3b740158-0a9c-4cab-ac46-6d774b62e8fb',
            }
        ]
    },
    development: {
        name: 'DEVELOPMENT',
        tdm: {
            name: 'Gowtham Kollati',
            role: 'Technical Delivery Manager',
            designation: 'Technical Delivery Manager',
            avatar: '/profiles/kollati.png',
            uuid: 'caea7c75-5d28-48cb-907a-abc84e507704',
        },
        members: [
            {
                name: 'Praveen Dommeti',
                role: 'Associate Trainee',
                avatar: '/profiles/praveen.png',
                uuid: 'aaa7331f-68e3-422e-90ef-ff11e55e0b40',
            }
        ]
    },
    management: {
        name: 'MANAGEMENT',
        sdm: {
            name: 'ARUN G',
            role: 'CEO',
            designation: 'CEO',
            avatar: '/profiles/arun.png',
            uuid: 'arun-g-ceo-uuid', // This should be replaced with actual UUID from database
        },
        members: []
    }
};
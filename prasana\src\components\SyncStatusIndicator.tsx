import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Alert<PERSON>riangle, Refresh<PERSON><PERSON>, RotateCcw } from 'lucide-react';
import { getSyncStatus } from '../utils/authSync';

interface SyncStatus {
  isInSync: boolean;
  authUserCount: number;
  employeeCount: number;
  issueCount: number;
}

const SyncStatusIndicator: React.FC = () => {
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    checkSyncStatus();
  }, []);

  const checkSyncStatus = async () => {
    setLoading(true);
    try {
      const status = await getSyncStatus();
      setSyncStatus(status);
    } catch (error) {
      console.error('Error checking sync status:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center space-x-2">
          <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />
          <span className="text-blue-800 text-sm">Checking sync status...</span>
        </div>
      </div>
    );
  }

  if (!syncStatus) {
    return null;
  }

  return (
    <div className={`border rounded-lg p-4 ${
      syncStatus.isInSync 
        ? 'bg-green-50 border-green-200' 
        : 'bg-yellow-50 border-yellow-200'
    }`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {syncStatus.isInSync ? (
            <CheckCircle className="w-5 h-5 text-green-600" />
          ) : (
            <AlertTriangle className="w-5 h-5 text-yellow-600" />
          )}
          <div>
            <h3 className={`font-medium ${
              syncStatus.isInSync ? 'text-green-800' : 'text-yellow-800'
            }`}>
              {syncStatus.isInSync ? 'Systems Synchronized' : 'Sync Issues Detected'}
            </h3>
            <p className={`text-sm ${
              syncStatus.isInSync ? 'text-green-600' : 'text-yellow-600'
            }`}>
              {syncStatus.isInSync 
                ? `${syncStatus.authUserCount} auth users, ${syncStatus.employeeCount} employees`
                : `${syncStatus.issueCount} issues found between auth and employee systems`
              }
            </p>
          </div>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={checkSyncStatus}
            className="p-2 text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100"
            title="Refresh sync status"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
          
          {!syncStatus.isInSync && (
            <button
              onClick={() => window.location.href = '#user-management'}
              className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700 flex items-center space-x-1"
            >
              <RotateCcw className="w-3 h-3" />
              <span>Fix Issues</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default SyncStatusIndicator;

import React, { useState, useEffect } from 'react';
import { supabase } from '../supabaseClient';
import { timesheetService } from '../services/timesheetService';
import { RefreshCw, User, Clock, Database } from 'lucide-react';

const TimesheetDebugger: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [timeEntries, setTimeEntries] = useState<any[]>([]);

  const runDebug = async () => {
    setLoading(true);
    const info: any = {};

    try {
      // 1. Check current auth user
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      info.authUser = {
        id: user?.id,
        email: user?.email,
        error: authError?.message
      };

      if (user) {
        // 2. Check employee record
        const { data: employee, error: empError } = await supabase
          .from('employees')
          .select('id, first_name, last_name, email, user_id')
          .eq('user_id', user.id)
          .single();

        info.employee = {
          data: employee,
          error: empError?.message
        };

        if (employee) {
          // 3. Check time entries directly from database
          const { data: dbEntries, error: dbError } = await supabase
            .from('time_entries')
            .select('*')
            .eq('employee_id', employee.id)
            .order('created_at', { ascending: false });

          info.directDbEntries = {
            count: dbEntries?.length || 0,
            data: dbEntries,
            error: dbError?.message
          };

          // 4. Check time entries via service
          const today = new Date();
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
          
          try {
            const serviceEntries = await timesheetService.getTimeEntries(
              weekAgo.toISOString().split('T')[0],
              today.toISOString().split('T')[0]
            );
            
            info.serviceEntries = {
              count: serviceEntries.length,
              data: serviceEntries
            };
            setTimeEntries(serviceEntries);
          } catch (serviceError: any) {
            info.serviceEntries = {
              error: serviceError.message
            };
          }

          // 5. Check if there are entries with different user_id
          const { data: allUserEntries, error: allError } = await supabase
            .from('time_entries')
            .select('id, user_id, employee_id, description, created_at')
            .eq('employee_id', employee.id);

          info.allUserEntries = {
            data: allUserEntries,
            error: allError?.message
          };
        }
      }

      setDebugInfo(info);
    } catch (error: any) {
      console.error('Debug error:', error);
      setDebugInfo({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    runDebug();
  }, []);

  const forceRefreshTimesheet = async () => {
    try {
      // Force refresh the timesheet service
      const today = new Date();
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      
      const entries = await timesheetService.getTimeEntries(
        weekAgo.toISOString().split('T')[0],
        today.toISOString().split('T')[0]
      );
      
      setTimeEntries(entries);
      console.log('Forced refresh - entries:', entries);
    } catch (error) {
      console.error('Force refresh error:', error);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Timesheet Debug Information</h2>
        <div className="flex space-x-2">
          <button
            onClick={runDebug}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-blue-400 flex items-center space-x-2"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Refresh Debug</span>
          </button>
          <button
            onClick={forceRefreshTimesheet}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2"
          >
            <Clock className="w-4 h-4" />
            <span>Force Refresh Timesheet</span>
          </button>
        </div>
      </div>

      {loading && (
        <div className="flex justify-center items-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Running diagnostics...</span>
        </div>
      )}

      {/* Auth User Info */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-center space-x-2 mb-2">
          <User className="w-5 h-5 text-blue-600" />
          <h3 className="font-medium text-blue-900">Current Auth User</h3>
        </div>
        <pre className="text-sm text-blue-800 bg-blue-100 p-2 rounded overflow-x-auto">
          {JSON.stringify(debugInfo.authUser, null, 2)}
        </pre>
      </div>

      {/* Employee Info */}
      <div className="mb-6 p-4 bg-green-50 rounded-lg">
        <div className="flex items-center space-x-2 mb-2">
          <Database className="w-5 h-5 text-green-600" />
          <h3 className="font-medium text-green-900">Employee Record</h3>
        </div>
        <pre className="text-sm text-green-800 bg-green-100 p-2 rounded overflow-x-auto">
          {JSON.stringify(debugInfo.employee, null, 2)}
        </pre>
      </div>

      {/* Direct DB Entries */}
      <div className="mb-6 p-4 bg-purple-50 rounded-lg">
        <div className="flex items-center space-x-2 mb-2">
          <Database className="w-5 h-5 text-purple-600" />
          <h3 className="font-medium text-purple-900">Direct Database Entries</h3>
        </div>
        <pre className="text-sm text-purple-800 bg-purple-100 p-2 rounded overflow-x-auto">
          {JSON.stringify(debugInfo.directDbEntries, null, 2)}
        </pre>
      </div>

      {/* Service Entries */}
      <div className="mb-6 p-4 bg-orange-50 rounded-lg">
        <div className="flex items-center space-x-2 mb-2">
          <Clock className="w-5 h-5 text-orange-600" />
          <h3 className="font-medium text-orange-900">Service Entries (Last 7 Days)</h3>
        </div>
        <pre className="text-sm text-orange-800 bg-orange-100 p-2 rounded overflow-x-auto">
          {JSON.stringify(debugInfo.serviceEntries, null, 2)}
        </pre>
      </div>

      {/* All User Entries */}
      <div className="mb-6 p-4 bg-red-50 rounded-lg">
        <div className="flex items-center space-x-2 mb-2">
          <Database className="w-5 h-5 text-red-600" />
          <h3 className="font-medium text-red-900">All Entries for Employee</h3>
        </div>
        <pre className="text-sm text-red-800 bg-red-100 p-2 rounded overflow-x-auto">
          {JSON.stringify(debugInfo.allUserEntries, null, 2)}
        </pre>
      </div>

      {/* Current Timesheet Service Results */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-medium text-gray-900 mb-2">Current Timesheet Service Results</h3>
        <p className="text-sm text-gray-600 mb-2">Entries found: {timeEntries.length}</p>
        {timeEntries.length > 0 && (
          <div className="space-y-2">
            {timeEntries.map((entry, index) => (
              <div key={entry.id || index} className="p-2 bg-white rounded border">
                <p><strong>Description:</strong> {entry.description}</p>
                <p><strong>Date:</strong> {entry.date || entry.entry_date}</p>
                <p><strong>Duration:</strong> {entry.duration} minutes</p>
                <p><strong>Category:</strong> {entry.category}</p>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TimesheetDebugger;

import { supabase } from '../supabaseClient';
import {
  HREmployeeProfile,
  HREmployeeBadge,
  HRLeaveRecord,
  HREmployeeSkill,
  HREmployeeDocument,
  HRFilterOptions,
  HRSearchOptions
} from '../types/hr';

// Fetch all employee profiles for HR (Super Admin only)
export const fetchHREmployeeProfiles = async (): Promise<HREmployeeProfile[]> => {
  try {
    // Fetch from employees table with proper joins
    const { data, error } = await supabase
      .from('employees')
      .select(`
        id,
        employee_id,
        first_name,
        last_name,
        email,
        phone,
        date_of_birth,
        department,
        designation,
        team_id,
        teams(
          id,
          name
        ),
        manager_id,
        joining_date,
        employment_type,
        status,
        location,
        current_address,
        emergency_contact_name,
        emergency_contact_phone,
        created_at,
        updated_at
      `)
      .order('first_name', { ascending: true });

    if (error) {
      console.error('Error fetching HR employee profiles:', error);
      throw error;
    }

    // Transform the data to match HREmployeeProfile interface
    const transformedData: HREmployeeProfile[] = (data || []).map((employee: any) => ({
      id: employee.id,
      employee_id: employee.employee_id,
      full_name: `${employee.first_name} ${employee.last_name}`,
      first_name: employee.first_name,
      last_name: employee.last_name,
      email: employee.email,
      phone: employee.phone,
      department: employee.department,
      designation: employee.designation,
      team: employee.teams?.name || employee.department, // Use team name from join
      manager_id: employee.manager_id,
      hire_date: employee.joining_date, // Correct field name
      employment_type: employee.employment_type,
      employment_status: employee.status, // Correct field name
      location: employee.location,
      salary: null, // Not exposed in basic query for security
      profile_picture_url: null, // Not implemented yet
      skills: [], // Not implemented yet
      emergency_contact_name: employee.emergency_contact_name,
      emergency_contact_phone: employee.emergency_contact_phone,
      address: employee.current_address, // Correct field name
      date_of_birth: employee.date_of_birth,
      created_at: employee.created_at,
      updated_at: employee.updated_at,
      // Default values for fields that might not exist in employees table
      badges: [],
      leave_records: [],
      documents: []
    }));

    // Filter out system administrator accounts from HR dashboard
    const filteredData = transformedData.filter(employee =>
      employee.employee_id !== 'EMP_SUPER_001' &&
      employee.designation !== 'System Administrator'
    );

    return filteredData;
  } catch (error) {
    console.error('Error in fetchHREmployeeProfiles:', error);
    throw error;
  }
};

// Fetch detailed badge information for an employee
export const fetchEmployeeBadgesHR = async (employeeId: string): Promise<HREmployeeBadge[]> => {
  try {
    // For now, return empty array since badges system is not fully implemented
    // This can be updated when badges table is available
    console.log('Fetching badges for employee:', employeeId);
    return [];
  } catch (error) {
    console.error('Error in fetchEmployeeBadgesHR:', error);
    return [];
  }
};

// Fetch leave history for an employee
export const fetchEmployeeLeaveHistoryHR = async (employeeId: string): Promise<HRLeaveRecord[]> => {
  try {
    // Try to fetch from leave_requests table if it exists
    const { data, error } = await supabase
      .from('leave_requests')
      .select('*')
      .eq('employee_id', employeeId)
      .order('created_at', { ascending: false });

    if (error) {
      console.log('Leave requests table not available:', error.message);
      return [];
    }

    // Transform to HRLeaveRecord format if data exists
    return (data || []).map(leave => ({
      id: leave.id,
      employee_id: employeeId,
      leave_type: leave.leave_type || 'Annual',
      start_date: leave.start_date,
      end_date: leave.end_date,
      days_requested: leave.days_requested || 1,
      status: leave.status || 'pending',
      reason: leave.reason || '',
      approved_by: leave.approved_by,
      approved_at: leave.approved_at,
      created_at: leave.created_at
    }));
  } catch (error) {
    console.error('Error in fetchEmployeeLeaveHistoryHR:', error);
    return [];
  }
};

// Fetch employee skills (if skills table exists)
export const fetchEmployeeSkillsHR = async (employeeId: string): Promise<HREmployeeSkill[]> => {
  try {
    // For now, return empty array since skills system is not fully implemented
    // This can be updated when employee_skills table is available
    console.log('Fetching skills for employee:', employeeId);
    return [];
  } catch (error) {
    console.error('Error in fetchEmployeeSkillsHR:', error);
    return [];
  }
};

// Filter and search employee profiles
export const filterHREmployeeProfiles = (
  profiles: HREmployeeProfile[],
  filters: HRFilterOptions,
  search: HRSearchOptions
): HREmployeeProfile[] => {
  let filtered = [...profiles];
  
  // Apply search
  if (search.query.trim()) {
    const query = search.query.toLowerCase();
    filtered = filtered.filter(profile => {
      return search.fields.some(field => {
        switch (field) {
          case 'name':
            return profile.full_name.toLowerCase().includes(query);
          case 'email':
            return profile.email.toLowerCase().includes(query);
          case 'designation':
            return profile.designation?.toLowerCase().includes(query);
          case 'department':
            return profile.department?.toLowerCase().includes(query);
          case 'employee_id':
            return profile.employee_id?.toLowerCase().includes(query);
          default:
            return false;
        }
      });
    });
  }
  
  // Apply filters
  if (filters.department) {
    filtered = filtered.filter(p => p.department === filters.department);
  }
  
  if (filters.team) {
    filtered = filtered.filter(p => p.team === filters.team);
  }
  
  if (filters.employment_type) {
    filtered = filtered.filter(p => p.employment_type === filters.employment_type);
  }
  
  if (filters.employment_status) {
    filtered = filtered.filter(p => p.employment_status === filters.employment_status);
  }
  
  if (filters.location) {
    filtered = filtered.filter(p => p.location === filters.location);
  }
  
  // Badge count filters - skip for now since badges are not implemented
  // if (filters.badge_count_min !== undefined) {
  //   filtered = filtered.filter(p => (p.badges?.length || 0) >= filters.badge_count_min!);
  // }

  // if (filters.badge_count_max !== undefined) {
  //   filtered = filtered.filter(p => (p.badges?.length || 0) <= filters.badge_count_max!);
  // }
  
  // Years of service calculation - calculate from hire_date
  if (filters.years_of_service_min !== undefined) {
    filtered = filtered.filter(p => {
      if (!p.hire_date) return false;
      const yearsOfService = new Date().getFullYear() - new Date(p.hire_date).getFullYear();
      return yearsOfService >= filters.years_of_service_min!;
    });
  }

  if (filters.years_of_service_max !== undefined) {
    filtered = filtered.filter(p => {
      if (!p.hire_date) return false;
      const yearsOfService = new Date().getFullYear() - new Date(p.hire_date).getFullYear();
      return yearsOfService <= filters.years_of_service_max!;
    });
  }

  if (filters.joining_date_from) {
    filtered = filtered.filter(p =>
      p.hire_date && p.hire_date >= filters.joining_date_from!
    );
  }

  if (filters.joining_date_to) {
    filtered = filtered.filter(p =>
      p.hire_date && p.hire_date <= filters.joining_date_to!
    );
  }
  
  return filtered;
};

// Get unique values for filter dropdowns
export const getHRFilterOptions = (profiles: HREmployeeProfile[]) => {
  return {
    departments: [...new Set(profiles.map(p => p.department).filter(Boolean))].sort(),
    teams: [...new Set(profiles.map(p => p.team_name).filter(Boolean))].sort(),
    employment_types: [...new Set(profiles.map(p => p.employment_type).filter(Boolean))].sort(),
    employment_statuses: [...new Set(profiles.map(p => p.employment_status).filter(Boolean))].sort(),
    locations: [...new Set(profiles.map(p => p.location).filter(Boolean))].sort(),
    managers: [...new Set(profiles.map(p => p.manager_name).filter(Boolean))].sort()
  };
};

// Export employee data to CSV
export const exportEmployeeDataToCSV = (
  profiles: HREmployeeProfile[],
  fields: (keyof HREmployeeProfile)[]
): string => {
  const headers = fields.join(',');
  const rows = profiles.map(profile => 
    fields.map(field => {
      const value = profile[field];
      // Escape commas and quotes in CSV
      if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value || '';
    }).join(',')
  );
  
  return [headers, ...rows].join('\n');
};

// Fetch employee documents for HR (Super Admin only)
export const fetchHREmployeeDocuments = async (employeeId: string): Promise<HREmployeeDocument[]> => {
  try {
    const { data, error } = await supabase
      .from('employee_documents')
      .select(`
        id,
        employee_id,
        document_name,
        document_type,
        file_path,
        file_size,
        mime_type,
        is_confidential,
        access_level,
        uploaded_by,
        uploaded_by_name,
        upload_date,
        expiry_date,
        status,
        created_at,
        updated_at
      `)
      .eq('employee_id', employeeId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching employee documents:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in fetchHREmployeeDocuments:', error);
    throw error;
  }
};

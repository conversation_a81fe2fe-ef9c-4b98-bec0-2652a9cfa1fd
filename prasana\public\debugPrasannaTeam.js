// Debug and fix Prasanna team assignment issue
// Run this in browser console to diagnose and fix the problem

async function debugPrasannaTeam() {
  console.log('🔍 Starting Prasanna team debug...');
  
  try {
    // Import supabase client
    const { supabase } = await import('/src/supabaseClient.js');
    
    if (!supabase) {
      throw new Error('Supabase client not available');
    }

    console.log('=== STEP 1: Check all teams ===');
    const { data: allTeams, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .eq('is_active', true);

    if (teamsError) {
      console.error('❌ Error fetching teams:', teamsError);
      throw teamsError;
    }

    console.log('✅ Available teams:', allTeams.map(t => ({ id: t.id, name: t.name })));

    const athenaTeam = allTeams.find(t => t.name === 'ATHENA');
    const titanTeam = allTeams.find(t => t.name === 'TITAN');

    console.log('🎯 ATHENA team:', athenaTeam);
    console.log('🎯 TITAN team:', titanTeam);

    console.log('=== STEP 2: Search for Prasanna ===');
    const { data: allPrasannas, error: prasannaError } = await supabase
      .from('employees')
      .select(`
        id,
        user_id,
        employee_id,
        first_name,
        last_name,
        email,
        designation,
        department,
        team_id,
        status,
        teams(id, name)
      `)
      .or('first_name.ilike.%prasanna%,last_name.ilike.%prasanna%,email.ilike.%prasanna%');

    if (prasannaError) {
      console.error('❌ Error searching for Prasanna:', prasannaError);
      throw prasannaError;
    }

    console.log('🔍 Found employees matching "Prasanna":', allPrasannas);

    if (allPrasannas.length === 0) {
      console.log('❌ No employees found with "Prasanna" in name or email');
      return { success: false, error: 'Prasanna not found in database' };
    }

    const prasanna = allPrasannas[0]; // Take the first match
    console.log('👤 Selected Prasanna:', {
      id: prasanna.id,
      name: `${prasanna.first_name} ${prasanna.last_name}`,
      email: prasanna.email,
      designation: prasanna.designation,
      department: prasanna.department,
      team_id: prasanna.team_id,
      currentTeam: prasanna.teams?.name,
      status: prasanna.status
    });

    console.log('=== STEP 3: Check ATHENA team members ===');
    const { data: athenaMembers, error: athenaMembersError } = await supabase
      .from('employees')
      .select(`
        id,
        first_name,
        last_name,
        designation,
        team_id
      `)
      .eq('team_id', athenaTeam.id)
      .eq('status', 'active');

    if (athenaMembersError) {
      console.error('❌ Error fetching ATHENA members:', athenaMembersError);
    } else {
      console.log('👥 Current ATHENA team members:', athenaMembers.map(m => ({
        name: `${m.first_name} ${m.last_name}`,
        designation: m.designation
      })));
    }

    console.log('=== STEP 4: Fix Prasanna team assignment ===');
    if (prasanna.team_id !== athenaTeam.id) {
      console.log(`🔄 Moving Prasanna from ${prasanna.teams?.name || 'unknown'} to ATHENA...`);
      
      const { error: updateError } = await supabase
        .from('employees')
        .update({
          team_id: athenaTeam.id,
          department: 'ATHENA'
        })
        .eq('id', prasanna.id);

      if (updateError) {
        console.error('❌ Error updating Prasanna:', updateError);
        throw updateError;
      }

      console.log('✅ Prasanna team updated successfully');
    } else {
      console.log('✅ Prasanna is already in ATHENA team');
    }

    console.log('=== STEP 5: Verify the fix ===');
    const { data: updatedAthenaMembers, error: verifyError } = await supabase
      .from('employees')
      .select(`
        id,
        first_name,
        last_name,
        designation,
        department,
        teams(name)
      `)
      .eq('team_id', athenaTeam.id)
      .eq('status', 'active');

    if (verifyError) {
      console.error('❌ Error verifying fix:', verifyError);
    } else {
      console.log('👥 Updated ATHENA team members:', updatedAthenaMembers.map(m => ({
        name: `${m.first_name} ${m.last_name}`,
        designation: m.designation,
        team: m.teams?.name
      })));

      const prasannaInAthena = updatedAthenaMembers.find(m => 
        m.first_name.toLowerCase().includes('prasanna')
      );

      if (prasannaInAthena) {
        console.log('🎉 SUCCESS! Prasanna is now in ATHENA team');
      } else {
        console.log('❌ Prasanna still not showing in ATHENA team');
      }
    }

    console.log('=== STEP 6: Test fetchTeamsWithMembers function ===');
    try {
      const { fetchTeamsWithMembers } = await import('/src/data/supabaseTeams.js');
      const teamsData = await fetchTeamsWithMembers();
      
      console.log('📊 Teams data from fetchTeamsWithMembers:');
      Object.entries(teamsData).forEach(([teamKey, teamData]) => {
        console.log(`${teamKey.toUpperCase()}:`, {
          name: teamData.name,
          memberCount: teamData.members?.length || 0,
          members: teamData.members?.map(m => m.name) || []
        });
      });

      const athenaTeamData = teamsData.athena || teamsData.ATHENA;
      if (athenaTeamData) {
        const prasannaInTeamData = athenaTeamData.members?.find(m => 
          m.name.toLowerCase().includes('prasanna')
        );
        
        if (prasannaInTeamData) {
          console.log('✅ Prasanna found in ATHENA team data:', prasannaInTeamData);
        } else {
          console.log('❌ Prasanna NOT found in ATHENA team data');
          console.log('ATHENA members:', athenaTeamData.members?.map(m => m.name));
        }
      }
    } catch (importError) {
      console.error('❌ Error testing fetchTeamsWithMembers:', importError);
    }

    console.log('🎉 Debug completed! Please refresh the Team Collaboration page.');

    return {
      success: true,
      prasanna: {
        id: prasanna.id,
        name: `${prasanna.first_name} ${prasanna.last_name}`,
        team: 'ATHENA'
      }
    };

  } catch (error) {
    console.error('❌ Debug failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Auto-load message
console.log('🚀 Prasanna Team Debug Script Loaded');
console.log('📋 Run: debugPrasannaTeam()');
console.log('🔄 This will diagnose and fix the issue');

// Make function globally available
window.debugPrasannaTeam = debugPrasannaTeam;

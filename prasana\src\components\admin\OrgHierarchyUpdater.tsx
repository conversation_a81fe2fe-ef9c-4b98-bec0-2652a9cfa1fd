import React, { useState } from 'react';
import { Play, CheckCircle, AlertCircle, Users, RefreshCw, Bug } from 'lucide-react';
import { createOrganizationalHierarchy, debugHierarchy } from '../../utils/updateOrgHierarchy';

const OrgHierarchyUpdater: React.FC = () => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateStatus, setUpdateStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [updateMessage, setUpdateMessage] = useState('');
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const handleUpdate = async () => {
    setIsUpdating(true);
    setUpdateStatus('idle');
    setLogs([]);
    setUpdateMessage('');

    try {
      addLog('🚀 Creating organizational hierarchy from scratch...');

      const result = await createOrganizationalHierarchy();

      addLog('✅ Hierarchy created successfully');
      addLog(`Total employees created: ${result.counts.total}`);
      addLog(`CEO: ${result.counts.ceo}, CRO: ${result.counts.cro}`);
      addLog(`SDMs: ${result.counts.sdm}, Management: ${result.counts.management}`);
      addLog(`Team Members: ${result.counts.team}`);

      setUpdateStatus('success');
      setUpdateMessage('Organizational hierarchy created successfully! Please refresh the page to see the new structure.');

      addLog('🔄 Please refresh the Team Directory page to see changes');

    } catch (error) {
      console.error('Creation failed:', error);
      addLog(`❌ Creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setUpdateStatus('error');
      setUpdateMessage(`Creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDebug = async () => {
    setLogs([]);
    addLog('🔍 Running hierarchy debug...');

    try {
      const result = await debugHierarchy();
      if (result.success) {
        addLog('✅ Debug completed - check console for detailed output');
      } else {
        addLog(`❌ Debug failed: ${result.error}`);
      }
    } catch (error) {
      addLog(`❌ Debug error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const clearLogs = () => {
    setLogs([]);
    setUpdateStatus('idle');
    setUpdateMessage('');
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-xl shadow-lg">
      <div className="flex items-center space-x-3 mb-6">
        <Users className="w-8 h-8 text-blue-600" />
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Organizational Hierarchy Creator</h2>
          <p className="text-gray-600">Create complete team structure with CEO → CRO → SDM → TDM/CXM → Team Members</p>
        </div>
      </div>

      {/* Creation Summary */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <h3 className="font-semibold text-blue-900 mb-2">What will be created:</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• <strong>CEO:</strong> ARUN G (Executive leadership)</li>
          <li>• <strong>CRO:</strong> Priyadarshini (Chief Revenue Officer)</li>
          <li>• <strong>4 SDMs:</strong> Eashwara Prasadh (NEXUS), Yuvaraj S (DYNAMIX), Aamina Begam T (TITAN), Sri Ram (ATHENA)</li>
          <li>• <strong>8 Managers:</strong> 4 TDMs and 4 CXMs across all departments</li>
          <li>• <strong>9 Team Members:</strong> Associate Trainees distributed across departments</li>
          <li>• <strong>Complete 5-level hierarchy</strong> with proper reporting relationships</li>
        </ul>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-4 mb-6">
        <button
          onClick={handleUpdate}
          disabled={isUpdating}
          className={`
            flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all
            ${isUpdating 
              ? 'bg-gray-400 cursor-not-allowed' 
              : 'bg-blue-600 hover:bg-blue-700 text-white'
            }
          `}
        >
          {isUpdating ? (
            <RefreshCw className="w-5 h-5 animate-spin" />
          ) : (
            <Play className="w-5 h-5" />
          )}
          <span>{isUpdating ? 'Creating...' : 'Create Hierarchy'}</span>
        </button>

        <button
          onClick={handleDebug}
          className="flex items-center space-x-2 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
        >
          <Bug className="w-4 h-4" />
          <span>Debug Hierarchy</span>
        </button>

        <button
          onClick={clearLogs}
          disabled={isUpdating}
          className="px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
        >
          Clear Logs
        </button>
      </div>

      {/* Status Message */}
      {updateMessage && (
        <div className={`
          flex items-center space-x-2 p-4 rounded-lg mb-6
          ${updateStatus === 'success' 
            ? 'bg-green-50 border border-green-200 text-green-800' 
            : 'bg-red-50 border border-red-200 text-red-800'
          }
        `}>
          {updateStatus === 'success' ? (
            <CheckCircle className="w-5 h-5" />
          ) : (
            <AlertCircle className="w-5 h-5" />
          )}
          <span>{updateMessage}</span>
        </div>
      )}

      {/* Logs */}
      {logs.length > 0 && (
        <div className="bg-gray-900 text-green-400 rounded-lg p-4 font-mono text-sm">
          <h3 className="text-white font-semibold mb-2">Update Logs:</h3>
          <div className="max-h-96 overflow-y-auto space-y-1">
            {logs.map((log, index) => (
              <div key={index} className="whitespace-pre-wrap">
                {log}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Expected Hierarchy Preview */}
      <div className="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 className="font-semibold text-gray-900 mb-4">Expected Hierarchy After Update:</h3>
        <div className="font-mono text-sm text-gray-700 space-y-1">
          <div>ARUN G (CEO)</div>
          <div className="ml-4">└── Priyadarshini (CRO)</div>
          <div className="ml-8">├── Eashwara Prasadh (NEXUS SDM)</div>
          <div className="ml-12">│   ├── Yusuf Fayas (TDM) → Gaushik, Hariharan</div>
          <div className="ml-12">│   └── Darshan K (CXM)</div>
          <div className="ml-8">├── Yuvaraj S (DYNAMIX SDM)</div>
          <div className="ml-12">│   ├── Purushoth (TDM) → Praveen, Nithish</div>
          <div className="ml-12">│   └── Kiyshore K (CXM)</div>
          <div className="ml-8">├── Aamina Begam T (TITAN SDM)</div>
          <div className="ml-12">│   ├── Gowtham Kollati (TDM) → Sivaranjani K, Fazeela M, Keerthipriya</div>
          <div className="ml-12">│   └── Prasanna (CXM)</div>
          <div className="ml-8">└── Sri Ram (ATHENA SDM)</div>
          <div className="ml-12">├── Selvendrane (TDM) → Kollati Gowtham, Shri Mathi</div>
          <div className="ml-12">└── Maheshwaran (CXM)</div>
        </div>
      </div>
    </div>
  );
};

export default OrgHierarchyUpdater;

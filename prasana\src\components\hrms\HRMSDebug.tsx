import React, { useState, useEffect } from 'react';
import { supabase } from '../../supabaseClient';
import OrgHierarchyUpdater from '../admin/OrgHierarchyUpdater';

const HRMSDebug: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    runDiagnostics();
  }, []);

  const runDiagnostics = async () => {
    const info: any = {};
    
    try {
      // Check auth
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      info.auth = {
        user: user ? { id: user.id, email: user.email } : null,
        error: authError?.message
      };

      if (user) {
        // Check employees table
        try {
          const { data: employees, error: empError } = await supabase
            .from('employees')
            .select('*')
            .eq('user_id', user.id);
          
          info.employees = {
            data: employees,
            error: empError?.message,
            count: employees?.length || 0
          };
        } catch (error) {
          info.employees = { error: (error as Error).message };
        }

        // Check leave types
        try {
          const { data: leaveTypes, error: ltError } = await supabase
            .from('leave_types')
            .select('*');
          
          info.leaveTypes = {
            data: leaveTypes,
            error: ltError?.message,
            count: leaveTypes?.length || 0
          };
        } catch (error) {
          info.leaveTypes = { error: (error as Error).message };
        }

        // Check holidays
        try {
          const { data: holidays, error: hError } = await supabase
            .from('holidays')
            .select('*');
          
          info.holidays = {
            data: holidays,
            error: hError?.message,
            count: holidays?.length || 0
          };
        } catch (error) {
          info.holidays = { error: (error as Error).message };
        }
      }

    } catch (error) {
      info.generalError = (error as Error).message;
    }

    setDebugInfo(info);
    setLoading(false);
  };

  const createEmployee = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const newEmployee = {
        user_id: user.id,
        employee_id: `EMP${Date.now()}`,
        first_name: user.user_metadata?.name || user.email?.split('@')[0] || 'User',
        last_name: 'Test',
        email: user.email || '',
        joining_date: new Date().toISOString().split('T')[0],
        status: 'active'
      };

      const { data, error } = await supabase
        .from('employees')
        .insert([newEmployee])
        .select()
        .single();

      if (error) {
        alert('Error creating employee: ' + error.message);
      } else {
        alert('Employee created successfully!');
        runDiagnostics();
      }
    } catch (error) {
      alert('Error: ' + (error as Error).message);
    }
  };

  if (loading) {
    return <div className="p-4">Running diagnostics...</div>;
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">HRMS Debug Information</h1>
      
      <div className="space-y-6">
        {/* Auth Info */}
        <div className="bg-white rounded-lg shadow p-4">
          <h2 className="text-lg font-semibold mb-2">Authentication</h2>
          <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
            {JSON.stringify(debugInfo.auth, null, 2)}
          </pre>
        </div>

        {/* Employees Info */}
        <div className="bg-white rounded-lg shadow p-4">
          <h2 className="text-lg font-semibold mb-2">Employees Table</h2>
          <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
            {JSON.stringify(debugInfo.employees, null, 2)}
          </pre>
          {debugInfo.employees?.count === 0 && (
            <button
              onClick={createEmployee}
              className="mt-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Create Employee Record
            </button>
          )}
        </div>

        {/* Leave Types Info */}
        <div className="bg-white rounded-lg shadow p-4">
          <h2 className="text-lg font-semibold mb-2">Leave Types</h2>
          <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
            {JSON.stringify(debugInfo.leaveTypes, null, 2)}
          </pre>
        </div>

        {/* Holidays Info */}
        <div className="bg-white rounded-lg shadow p-4">
          <h2 className="text-lg font-semibold mb-2">Holidays</h2>
          <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
            {JSON.stringify(debugInfo.holidays, null, 2)}
          </pre>
        </div>

        {/* General Error */}
        {debugInfo.generalError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h2 className="text-lg font-semibold mb-2 text-red-800">General Error</h2>
            <p className="text-red-600">{debugInfo.generalError}</p>
          </div>
        )}

        {/* Organizational Hierarchy Updater */}
        <div className="bg-white rounded-lg shadow p-4">
          <h2 className="text-lg font-semibold mb-4">Organizational Hierarchy Update</h2>
          <OrgHierarchyUpdater />
        </div>

        {/* Actions */}
        <div className="bg-white rounded-lg shadow p-4">
          <h2 className="text-lg font-semibold mb-2">Actions</h2>
          <div className="space-x-2">
            <button
              onClick={runDiagnostics}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              Refresh Diagnostics
            </button>
            <button
              onClick={() => window.location.href = '/'}
              className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
            >
              Back to App
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HRMSDebug;

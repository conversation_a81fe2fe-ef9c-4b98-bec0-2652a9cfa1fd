-- =====================================================
-- TRIGGERS AND FUNCTIONS FOR AUTOMATION
-- =====================================================

-- =====================================================
-- 1. UPDATED_AT TRIGGER FUNCTION
-- =====================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_employees_updated_at BEFORE UPDATE ON employees
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_time_entries_updated_at BEFORE UPDATE ON time_entries
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_leave_applications_updated_at BEFORE UPDATE ON leave_applications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_employee_leave_balances_updated_at BEFORE UPDATE ON employee_leave_balances
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_attendance_updated_at BEFORE UPDATE ON attendance
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payroll_updated_at BEFORE UPDATE ON payroll
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_performance_reviews_updated_at BEFORE UPDATE ON performance_reviews
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_performance_goals_updated_at BEFORE UPDATE ON performance_goals
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_employee_documents_updated_at BEFORE UPDATE ON employee_documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_employee_badges_updated_at BEFORE UPDATE ON employee_badges
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 2. AUTOMATIC USER CREATION TRIGGER
-- =====================================================

-- Function to create user record when auth user is created
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO users (id, email, full_name, created_at)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        NEW.created_at
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- =====================================================
-- 3. LEAVE BALANCE CALCULATION FUNCTIONS
-- =====================================================

-- Function to calculate and update leave balance after leave approval
CREATE OR REPLACE FUNCTION update_leave_balance_on_approval()
RETURNS TRIGGER AS $$
BEGIN
    -- Only update if status changed to approved
    IF OLD.status != 'approved' AND NEW.status = 'approved' THEN
        UPDATE employee_leave_balances 
        SET used_days = used_days + NEW.total_days
        WHERE employee_id = NEW.employee_id 
        AND leave_type_id = NEW.leave_type_id 
        AND year = EXTRACT(YEAR FROM NEW.start_date);
    END IF;
    
    -- If status changed from approved to rejected/cancelled, restore balance
    IF OLD.status = 'approved' AND NEW.status IN ('rejected', 'cancelled') THEN
        UPDATE employee_leave_balances 
        SET used_days = used_days - NEW.total_days
        WHERE employee_id = NEW.employee_id 
        AND leave_type_id = NEW.leave_type_id 
        AND year = EXTRACT(YEAR FROM NEW.start_date);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for leave balance updates
CREATE TRIGGER update_leave_balance_trigger
    AFTER UPDATE ON leave_applications
    FOR EACH ROW EXECUTE FUNCTION update_leave_balance_on_approval();

-- =====================================================
-- 4. NOTIFICATION CREATION FUNCTIONS
-- =====================================================

-- Function to create notification
CREATE OR REPLACE FUNCTION create_notification(
    p_recipient_id UUID,
    p_title VARCHAR,
    p_message TEXT,
    p_type VARCHAR DEFAULT 'info',
    p_category VARCHAR DEFAULT 'system',
    p_action_url TEXT DEFAULT NULL,
    p_action_label VARCHAR DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO notifications (
        recipient_id, title, message, type, category, 
        action_url, action_label
    ) VALUES (
        p_recipient_id, p_title, p_message, p_type, p_category,
        p_action_url, p_action_label
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql;

-- Function to notify on leave application status change
CREATE OR REPLACE FUNCTION notify_leave_status_change()
RETURNS TRIGGER AS $$
DECLARE
    employee_user_id UUID;
    notification_title VARCHAR;
    notification_message TEXT;
BEGIN
    -- Get user_id for the employee
    SELECT user_id INTO employee_user_id 
    FROM employees 
    WHERE id = NEW.employee_id;
    
    -- Only notify on status changes
    IF OLD.status != NEW.status THEN
        CASE NEW.status
            WHEN 'approved' THEN
                notification_title := 'Leave Application Approved';
                notification_message := 'Your leave application from ' || NEW.start_date || ' to ' || NEW.end_date || ' has been approved.';
            WHEN 'rejected' THEN
                notification_title := 'Leave Application Rejected';
                notification_message := 'Your leave application from ' || NEW.start_date || ' to ' || NEW.end_date || ' has been rejected. Reason: ' || COALESCE(NEW.rejection_reason, 'No reason provided');
            ELSE
                RETURN NEW; -- No notification for other status changes
        END CASE;
        
        PERFORM create_notification(
            employee_user_id,
            notification_title,
            notification_message,
            CASE NEW.status WHEN 'approved' THEN 'success' ELSE 'warning' END,
            'leave',
            '/hrms/leave-applications',
            'View Leave Applications'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for leave status notifications
CREATE TRIGGER notify_leave_status_trigger
    AFTER UPDATE ON leave_applications
    FOR EACH ROW EXECUTE FUNCTION notify_leave_status_change();

-- =====================================================
-- 5. TIMESHEET APPROVAL NOTIFICATIONS
-- =====================================================

-- Function to notify on timesheet approval/rejection
CREATE OR REPLACE FUNCTION notify_timesheet_status_change()
RETURNS TRIGGER AS $$
DECLARE
    employee_user_id UUID;
    notification_title VARCHAR;
    notification_message TEXT;
BEGIN
    -- Get user_id for the employee
    SELECT user_id INTO employee_user_id 
    FROM employees 
    WHERE id = NEW.employee_id;
    
    -- Only notify on status changes to approved or rejected
    IF OLD.status != NEW.status AND NEW.status IN ('approved', 'rejected') THEN
        CASE NEW.status
            WHEN 'approved' THEN
                notification_title := 'Timesheet Approved';
                notification_message := 'Your timesheet entry for ' || NEW.entry_date || ' has been approved.';
            WHEN 'rejected' THEN
                notification_title := 'Timesheet Rejected';
                notification_message := 'Your timesheet entry for ' || NEW.entry_date || ' has been rejected. Reason: ' || COALESCE(NEW.rejection_reason, 'No reason provided');
        END CASE;
        
        PERFORM create_notification(
            employee_user_id,
            notification_title,
            notification_message,
            CASE NEW.status WHEN 'approved' THEN 'success' ELSE 'warning' END,
            'timesheet',
            '/timesheet',
            'View Timesheets'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for timesheet status notifications
CREATE TRIGGER notify_timesheet_status_trigger
    AFTER UPDATE ON time_entries
    FOR EACH ROW EXECUTE FUNCTION notify_timesheet_status_change();

-- =====================================================
-- 6. BADGE EXPIRY NOTIFICATION FUNCTION
-- =====================================================

-- Function to check and notify about expiring badges
CREATE OR REPLACE FUNCTION check_expiring_badges()
RETURNS void AS $$
DECLARE
    badge_record RECORD;
    employee_user_id UUID;
    days_until_expiry INTEGER;
BEGIN
    -- Check badges expiring in the next 30 days
    FOR badge_record IN
        SELECT eb.*, e.user_id, b.name as badge_name
        FROM employee_badges eb
        JOIN employees e ON eb.employee_id = e.id
        JOIN badges b ON eb.badge_id = b.id
        WHERE eb.status = 'active'
        AND eb.expiry_date IS NOT NULL
        AND eb.expiry_date BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '30 days'
    LOOP
        days_until_expiry := badge_record.expiry_date - CURRENT_DATE;
        
        PERFORM create_notification(
            badge_record.user_id,
            'Badge Expiring Soon',
            'Your ' || badge_record.badge_name || ' badge will expire in ' || days_until_expiry || ' days on ' || badge_record.expiry_date || '.',
            'warning',
            'badge',
            '/profile/badges',
            'View Badges'
        );
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. AUDIT LOG TRIGGER
-- =====================================================

-- Function to log changes to important tables
CREATE OR REPLACE FUNCTION log_table_changes()
RETURNS TRIGGER AS $$
DECLARE
    table_name_var VARCHAR := TG_TABLE_NAME;
    record_id_var UUID;
    old_values_var JSONB := NULL;
    new_values_var JSONB := NULL;
BEGIN
    -- Get record ID based on operation
    IF TG_OP = 'DELETE' THEN
        record_id_var := OLD.id;
        old_values_var := to_jsonb(OLD);
    ELSIF TG_OP = 'UPDATE' THEN
        record_id_var := NEW.id;
        old_values_var := to_jsonb(OLD);
        new_values_var := to_jsonb(NEW);
    ELSIF TG_OP = 'INSERT' THEN
        record_id_var := NEW.id;
        new_values_var := to_jsonb(NEW);
    END IF;
    
    -- Insert audit log entry
    INSERT INTO audit_log (
        table_name, record_id, action, old_values, new_values, changed_by
    ) VALUES (
        table_name_var, record_id_var, TG_OP, old_values_var, new_values_var, auth.uid()
    );
    
    -- Return appropriate record
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Apply audit triggers to sensitive tables
CREATE TRIGGER audit_employees_trigger
    AFTER INSERT OR UPDATE OR DELETE ON employees
    FOR EACH ROW EXECUTE FUNCTION log_table_changes();

CREATE TRIGGER audit_user_role_assignments_trigger
    AFTER INSERT OR UPDATE OR DELETE ON user_role_assignments
    FOR EACH ROW EXECUTE FUNCTION log_table_changes();

CREATE TRIGGER audit_payroll_trigger
    AFTER INSERT OR UPDATE OR DELETE ON payroll
    FOR EACH ROW EXECUTE FUNCTION log_table_changes();

-- =====================================================
-- 8. GRANT PERMISSIONS
-- =====================================================

-- Grant necessary permissions to authenticated users
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- Grant permissions for the helper functions
GRANT EXECUTE ON FUNCTION get_user_role_level(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION is_project_team_member(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_employee_id(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION create_employee_with_role_and_badges(UUID, VARCHAR, VARCHAR, VARCHAR, VARCHAR, VARCHAR, VARCHAR, VARCHAR, TEXT[]) TO authenticated;
GRANT EXECUTE ON FUNCTION create_notification(UUID, VARCHAR, TEXT, VARCHAR, VARCHAR, TEXT, VARCHAR) TO authenticated;

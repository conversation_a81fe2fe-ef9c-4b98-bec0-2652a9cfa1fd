-- Fix <PERSON><PERSON>'s team assignment from TITAN to ATHENA
-- This aligns the database with the static teams data

-- Get ATHENA team ID
DO $$
DECLARE
    athena_team_id UUID;
    prasanna_employee_id UUID;
BEGIN
    -- Get ATHENA team ID
    SELECT id INTO athena_team_id 
    FROM teams 
    WHERE name = 'ATHEN<PERSON>';
    
    -- Get <PERSON><PERSON><PERSON>'s employee ID
    SELECT id INTO prasanna_employee_id 
    FROM employees 
    WHERE first_name = '<PERSON><PERSON><PERSON>' 
    AND email = '<EMAIL>';
    
    -- Update <PERSON><PERSON><PERSON>'s team assignment
    IF athena_team_id IS NOT NULL AND prasanna_employee_id IS NOT NULL THEN
        UPDATE employees 
        SET team_id = athena_team_id,
            department = 'ATHENA',
            updated_at = NOW()
        WHERE id = prasanna_employee_id;
        
        RAISE NOTICE 'Successfully moved <PERSON><PERSON><PERSON> from TITAN to ATHENA team';
    ELSE
        RAISE NOTICE 'Could not find ATHENA team or <PERSON>rasanna employee';
    END IF;
END $$;

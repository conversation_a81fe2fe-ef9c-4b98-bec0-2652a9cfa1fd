import React, { useState, useEffect } from 'react';

interface Route {
  path: string;
  component: React.ComponentType<any>;
  exact?: boolean;
}

interface SimpleRouterProps {
  routes: Route[];
  fallback?: React.ComponentType<any>;
}

export const SimpleRouter: React.FC<SimpleRouterProps> = ({ routes, fallback: Fallback }) => {
  const [currentPath, setCurrentPath] = useState(window.location.pathname);

  useEffect(() => {
    const handlePopState = () => {
      setCurrentPath(window.location.pathname);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  const navigate = (path: string) => {
    window.history.pushState({}, '', path);
    setCurrentPath(path);
  };

  // Find matching route
  const matchedRoute = routes.find(route => {
    if (route.exact) {
      return route.path === currentPath;
    }
    return currentPath.startsWith(route.path);
  });

  if (matchedRoute) {
    const Component = matchedRoute.component;
    return <Component navigate={navigate} />;
  }

  if (Fallback) {
    return <Fallback navigate={navigate} />;
  }

  return <div>404 - Page not found</div>;
};

// Hook to use navigation
export const useNavigate = () => {
  const navigate = (path: string) => {
    window.history.pushState({}, '', path);
    window.dispatchEvent(new PopStateEvent('popstate'));
  };

  return navigate;
};

// Hook to get current location
export const useLocation = () => {
  const [pathname, setPathname] = useState(window.location.pathname);

  useEffect(() => {
    const handlePopState = () => {
      setPathname(window.location.pathname);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  return { pathname };
};

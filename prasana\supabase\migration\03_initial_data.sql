-- =====================================================
-- INITIAL DATA SETUP
-- SYSTEM ROLES, TEAMS, AND TEAM MEMBERS
-- =====================================================

-- =====================================================
-- 1. SYSTEM ROLES SETUP
-- =====================================================

INSERT INTO system_roles (name, description, level, permissions) VALUES
('User', 'Basic user with limited access', 1, '[
    "view_own_profile",
    "edit_own_profile", 
    "view_team_timesheets",
    "edit_own_timesheets",
    "view_own_badges",
    "view_projects_assigned",
    "view_own_attendance",
    "view_own_leave_balance",
    "apply_for_leave",
    "view_own_documents",
    "view_own_performance_reviews",
    "view_own_goals"
]'),

('Admin', 'Administrator with team management capabilities', 2, '[
    "view_own_profile",
    "edit_own_profile",
    "view_team_timesheets",
    "edit_own_timesheets",
    "approve_team_timesheets",
    "view_own_badges",
    "assign_badges_to_team",
    "view_projects_assigned",
    "create_projects",
    "edit_projects",
    "manage_project_members",
    "view_team_attendance",
    "view_own_leave_balance",
    "apply_for_leave",
    "view_team_performance",
    "conduct_performance_reviews",
    "view_team_documents",
    "view_team_reports",
    "manage_project_milestones"
]'),

('Superadmin', 'Super administrator with full system access', 3, '[
    "view_own_profile",
    "edit_own_profile",
    "view_all_timesheets",
    "edit_own_timesheets",
    "approve_all_timesheets",
    "view_own_badges",
    "assign_badges_to_all",
    "manage_all_badges",
    "view_all_projects",
    "create_projects",
    "edit_projects",
    "delete_projects",
    "manage_all_project_members",
    "view_all_attendance",
    "view_own_leave_balance",
    "apply_for_leave",
    "approve_all_leave_applications",
    "process_payroll",
    "manage_user_roles",
    "view_all_performance_data",
    "conduct_performance_reviews",
    "view_all_documents",
    "generate_company_reports",
    "manage_system_settings",
    "manage_organizational_structure",
    "configure_leave_types",
    "manage_holiday_calendar",
    "system_administration"
]');

-- =====================================================
-- 2. TEAMS SETUP
-- =====================================================

INSERT INTO teams (id, name, description) VALUES
(uuid_generate_v4(), 'ATHENA', 'ATHENA Team - Service Delivery and Leadership'),
(uuid_generate_v4(), 'DYNAMIX', 'DYNAMIX Team - Technical Account Management'),
(uuid_generate_v4(), 'NEXUS', 'NEXUS Team - Client Experience and Leadership'),
(uuid_generate_v4(), 'TITAN', 'TITAN Team - Service Delivery and Management');

-- =====================================================
-- 3. LEAVE TYPES SETUP
-- =====================================================

INSERT INTO leave_types (name, code, description, max_days_per_year, carry_forward_allowed, max_carry_forward_days) VALUES
('Casual Leave', 'CL', 'Casual leave for personal matters', 12, true, 5),
('Sick Leave', 'SL', 'Medical leave for health issues', 12, false, 0),
('Annual Leave', 'AL', 'Annual vacation leave', 21, true, 10),
('Maternity Leave', 'ML', 'Maternity leave for new mothers', 90, false, 0),
('Paternity Leave', 'PL', 'Paternity leave for new fathers', 15, false, 0),
('Emergency Leave', 'EL', 'Emergency leave for urgent situations', 5, false, 0),
('Study Leave', 'STL', 'Leave for educational purposes', 10, false, 0);

-- =====================================================
-- 4. COMPANY HOLIDAYS SETUP
-- =====================================================

INSERT INTO holidays (name, date, type, description) VALUES
('New Year''s Day', '2024-01-01', 'public', 'New Year celebration'),
('Republic Day', '2024-01-26', 'public', 'Indian Republic Day'),
('Holi', '2024-03-25', 'public', 'Festival of Colors'),
('Good Friday', '2024-03-29', 'public', 'Christian holiday'),
('Independence Day', '2024-08-15', 'public', 'Indian Independence Day'),
('Gandhi Jayanti', '2024-10-02', 'public', 'Mahatma Gandhi''s Birthday'),
('Diwali', '2024-11-01', 'public', 'Festival of Lights'),
('Christmas Day', '2024-12-25', 'public', 'Christian holiday');

-- =====================================================
-- 5. BADGES/CERTIFICATIONS SETUP
-- =====================================================

INSERT INTO badges (name, description, category, has_expiry, default_validity_months, color) VALUES
('Associate Trainee', 'Entry-level position badge', 'Position', false, null, '#10B981'),
('Leadership', 'Leadership role certification', 'Leadership', false, null, '#F59E0B'),
('Service Delivery Manager', 'Service delivery management certification', 'Management', true, 24, '#EF4444'),
('Technical Account Manager', 'Technical account management certification', 'Management', true, 24, '#3B82F6'),
('Client Experience Manager', 'Client experience management certification', 'Management', true, 24, '#8B5CF6'),
('Team Lead', 'Team leadership certification', 'Leadership', true, 12, '#F97316'),
('Senior Developer', 'Senior development skills certification', 'Technical', true, 36, '#06B6D4'),
('Project Manager', 'Project management certification', 'Management', true, 24, '#84CC16'),
('Quality Assurance', 'Quality assurance certification', 'Technical', true, 18, '#EC4899'),
('DevOps Engineer', 'DevOps engineering certification', 'Technical', true, 24, '#6366F1');

-- =====================================================
-- 6. SYSTEM SETTINGS SETUP
-- =====================================================

INSERT INTO system_settings (setting_key, setting_value, description, category, is_public) VALUES
('company_name', '"TechnoSprint Solutions"', 'Company name', 'general', true),
('company_logo', '"/logo.png"', 'Company logo URL', 'general', true),
('working_hours_per_day', '8', 'Standard working hours per day', 'attendance', true),
('working_days_per_week', '5', 'Standard working days per week', 'attendance', true),
('overtime_rate_multiplier', '1.5', 'Overtime rate multiplier', 'payroll', false),
('late_arrival_grace_minutes', '15', 'Grace period for late arrival in minutes', 'attendance', true),
('timesheet_approval_required', 'true', 'Whether timesheet approval is required', 'timesheet', true),
('leave_approval_required', 'true', 'Whether leave approval is required', 'leave', true),
('max_consecutive_leave_days', '30', 'Maximum consecutive leave days allowed', 'leave', true),
('probation_period_months', '6', 'Probation period in months', 'hr', false),
('performance_review_frequency', '"annual"', 'Performance review frequency', 'performance', false),
('notification_email_enabled', 'true', 'Whether email notifications are enabled', 'notifications', false);

-- =====================================================
-- 7. SAMPLE PROJECTS SETUP
-- =====================================================

-- Get team IDs for reference
DO $$
DECLARE
    athena_team_id UUID;
    dynamix_team_id UUID;
    nexus_team_id UUID;
    titan_team_id UUID;
BEGIN
    SELECT id INTO athena_team_id FROM teams WHERE name = 'ATHENA';
    SELECT id INTO dynamix_team_id FROM teams WHERE name = 'DYNAMIX';
    SELECT id INTO nexus_team_id FROM teams WHERE name = 'NEXUS';
    SELECT id INTO titan_team_id FROM teams WHERE name = 'TITAN';

    -- Insert sample projects
    INSERT INTO projects (name, description, client_name, status, start_date, end_date, team_id, has_tasks_module, has_timesheet_module, is_billable) VALUES
    ('E-Commerce Platform Development', 'Complete e-commerce solution with payment integration', 'TechCorp Inc.', 'active', '2024-01-15', '2024-06-30', athena_team_id, true, true, true),
    ('Mobile App Redesign', 'UI/UX redesign for mobile application', 'StartupXYZ', 'active', '2024-02-01', '2024-05-15', dynamix_team_id, true, true, true),
    ('Data Analytics Dashboard', 'Business intelligence dashboard development', 'DataCorp Ltd.', 'active', '2024-01-20', '2024-07-20', nexus_team_id, true, true, true),
    ('Cloud Migration Project', 'Migration of legacy systems to cloud infrastructure', 'Enterprise Solutions', 'planning', '2024-03-01', '2024-09-30', titan_team_id, true, true, true),
    ('Internal HRMS Development', 'Development of internal HR management system', 'Internal Project', 'active', '2024-01-01', '2024-12-31', athena_team_id, true, true, false);
END $$;

-- =====================================================
-- 8. NOTIFICATION TEMPLATES
-- =====================================================

-- Note: Team member data will be inserted when users register
-- The following is a template for the team structure that will be created:

/*
TEAM STRUCTURE TO BE CREATED:

ATHENA Team:
- Sri Ram (Service Delivery Manager, Associate Trainee, Leadership)
- Selvendrane (Technical Account Manager, Associate Trainee, Leadership)  
- Maheshwaran (Client Experience Manager, Associate Trainee, Leadership)
- Theepatharan (Associate Trainee)
- Fazeela (Associate Trainee)
- Sivaranjani (Associate Trainee)

DYNAMIX Team:
- Yuvaraj (Service Delivery Manager, Associate Trainee, Leadership)
- Purushoth (Technical Account Manager, Associate Trainee, Leadership)
- Kiyshore K (Client Experience Manager, Associate Trainee, Leadership)
- Praveen Dommeti (Associate Trainee)

NEXUS Team:
- Eashwara Prasadh (Service Delivery Manager, Leadership)
- Yusuf Fayas (Technical Account Manager, Associate Trainee, Leadership)
- Darshan K (Client Experience Manager, Associate Trainee, Leadership)
- Gaushik (Associate Trainee)
- Sakthivel (Associate Trainee)

TITAN Team:
- Aamina Begam T (Service Delivery Manager, Associate Trainee, Leadership)
- Gowtham Kollati (Technical Account Manager, Associate Trainee, Leadership)
- Prasanna (Client Experience Manager, Associate Trainee, Leadership)
- Yamini (Associate Trainee)
- Shri Mathi (Associate Trainee)

Initial Role Assignments:
- All team members will start with "User" role
- Service Delivery Managers, Technical Account Managers, and Client Experience Managers will be assigned "Admin" role
- One Superadmin will be designated for system administration
*/

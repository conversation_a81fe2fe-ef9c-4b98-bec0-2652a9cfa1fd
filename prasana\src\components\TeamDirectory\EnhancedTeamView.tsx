import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Users, Crown, Shield, Star } from 'lucide-react';
import { TeamMember } from '../../types/team';
import { teams } from '../../data/teams';
import ProfileAvatar from '../ProfileAvatar';

interface EnhancedTeamViewProps {
  employees: TeamMember[];
  onEmployeeClick: (employee: TeamMember) => void;
}

interface TeamStructure {
  name: string;
  logo: string;
  color: string;
  ceo?: TeamMember;
  cro?: TeamMember;
  sdm?: TeamMember;
  tdm?: TeamMember;
  cxm?: TeamMember;
  members: TeamMember[];
}

const teamColors = {
  'TITAN': 'bg-blue-50 border-blue-200',
  'NEXUS': 'bg-purple-50 border-purple-200',
  'ATHENA': 'bg-green-50 border-green-200',
  'DYNAMIX': 'bg-amber-50 border-amber-200',
  'DEVELOPMENT': 'bg-indigo-50 border-indigo-200',
  'MANAGEMENT': 'bg-red-50 border-red-200'
};

const EnhancedTeamView: React.FC<EnhancedTeamViewProps> = ({ employees, onEmployeeClick }) => {
  const [expandedTeams, setExpandedTeams] = useState<Set<string>>(new Set(['TITAN', 'NEXUS', 'ATHENA', 'DYNAMIX']));

  const toggleTeam = (teamName: string) => {
    const newExpanded = new Set(expandedTeams);
    if (newExpanded.has(teamName)) {
      newExpanded.delete(teamName);
    } else {
      newExpanded.add(teamName);
    }
    setExpandedTeams(newExpanded);
  };

  // Organize employees by team
  const organizeByTeam = (): TeamStructure[] => {
    const teamStructures: TeamStructure[] = [];
    
    // Process each team from our teams data
    Object.entries(teams).forEach(([teamKey, teamData]) => {
      const teamName = teamData.name;
      const teamEmployees = employees.filter(emp => 
        emp.team === teamName || emp.department === teamName
      );

      if (teamEmployees.length === 0) return;

      // Find leadership roles
      const ceo = teamEmployees.find(emp =>
        emp.designation === 'CEO' ||
        (teamData.ceo && emp.name.toLowerCase().includes(teamData.ceo.name.toLowerCase()))
      );

      const cro = teamEmployees.find(emp =>
        emp.designation === 'Chief Revenue Officer' ||
        (teamData.cro && emp.name.toLowerCase().includes(teamData.cro.name.toLowerCase()))
      );

      const sdm = teamEmployees.find(emp =>
        emp.designation === 'Service Delivery Manager' ||
        (teamData.sdm && emp.name.toLowerCase().includes(teamData.sdm.name.toLowerCase()))
      );

      const tdm = teamEmployees.find(emp =>
        emp.designation === 'Technical Delivery Manager' ||
        (teamData.tdm && emp.name.toLowerCase().includes(teamData.tdm.name.toLowerCase()))
      );

      const cxm = teamEmployees.find(emp =>
        emp.designation === 'Client Experience Manager' ||
        (teamData.cxm && emp.name.toLowerCase().includes(teamData.cxm.name.toLowerCase()))
      );

      // Get regular members (excluding leadership)
      const members = teamEmployees.filter(emp =>
        emp !== ceo && emp !== cro && emp !== sdm && emp !== tdm && emp !== cxm
      );

      teamStructures.push({
        name: teamName,
        logo: teamName === 'DEVELOPMENT' ? '/profiles/developmentteam.png' : `/profiles/${teamName.toLowerCase()}.png`,
        color: teamColors[teamName as keyof typeof teamColors] || 'bg-gray-50 border-gray-200',
        ceo,
        cro,
        sdm,
        tdm,
        cxm,
        members
      });
    });

    // Handle MANAGEMENT team separately (only if not already processed)
    const hasManagementTeam = teamStructures.some(team => team.name === 'MANAGEMENT');

    if (!hasManagementTeam) {
      const managementEmployees = employees.filter(emp =>
        emp.team === 'MANAGEMENT' || emp.designation === 'CEO' || emp.designation === 'Chief Revenue Officer'
      );

      if (managementEmployees.length > 0) {
        const ceo = managementEmployees.find(emp => emp.designation === 'CEO');
        const cro = managementEmployees.find(emp => emp.designation === 'Chief Revenue Officer');
        const otherManagement = managementEmployees.filter(emp => emp !== ceo && emp !== cro);

        teamStructures.push({
          name: 'MANAGEMENT',
          logo: '',
          color: teamColors['MANAGEMENT'],
          ceo,
          cro,
          members: otherManagement
        });
      }
    }

    return teamStructures;
  };

  const teamStructures = organizeByTeam();

  const renderLeadershipCard = (leader: TeamMember, role: string, icon: React.ReactNode) => (
    <div 
      key={leader.id}
      onClick={() => onEmployeeClick(leader)}
      className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-all cursor-pointer group"
    >
      <div className="flex items-center space-x-3">
        <div className="relative">
          <ProfileAvatar 
            name={leader.name} 
            avatar={leader.avatar} 
            size="md" 
            team={leader.team as any}
          />
          <div className="absolute -top-1 -right-1 bg-yellow-400 rounded-full p-1">
            {icon}
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="font-semibold text-gray-900 truncate group-hover:text-blue-600">
            {leader.name}
          </h4>
          <p className="text-sm text-blue-600 font-medium">{role}</p>
          <p className="text-xs text-gray-500">{leader.designation}</p>
          {leader.employeeCode && (
            <p className="text-xs text-gray-400">PS: {leader.employeeCode}</p>
          )}
        </div>
      </div>
    </div>
  );

  const renderMemberCard = (member: TeamMember) => (
    <div 
      key={member.id}
      onClick={() => onEmployeeClick(member)}
      className="bg-white p-3 rounded-lg border border-gray-200 hover:shadow-md transition-all cursor-pointer group"
    >
      <div className="flex items-center space-x-3">
        <ProfileAvatar 
          name={member.name} 
          avatar={member.avatar} 
          size="sm" 
          team={member.team as any}
        />
        <div className="flex-1 min-w-0">
          <h5 className="font-medium text-gray-900 truncate group-hover:text-blue-600">
            {member.name}
          </h5>
          <p className="text-sm text-gray-600">{member.role}</p>
          {member.employeeCode && (
            <p className="text-xs text-gray-400">PS: {member.employeeCode}</p>
          )}
        </div>
        {member.status !== 'active' && (
          <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
            {member.status}
          </span>
        )}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {teamStructures.map((team, index) => {
        const isExpanded = expandedTeams.has(team.name);
        const totalMembers = (team.ceo ? 1 : 0) + (team.cro ? 1 : 0) + (team.sdm ? 1 : 0) + (team.tdm ? 1 : 0) + (team.cxm ? 1 : 0) + team.members.length;

        return (
          <div key={`${team.name}-${index}`} className={`rounded-xl border-2 ${team.color} overflow-hidden`}>
            {/* Team Header */}
            <div 
              className="p-6 cursor-pointer hover:bg-opacity-80 transition-colors"
              onClick={() => toggleTeam(team.name)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 flex items-center justify-center">
                    {team.name === 'MANAGEMENT' ? (
                      <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <Users className="w-6 h-6 text-red-600" />
                      </div>
                    ) : (
                      <img 
                        src={team.logo} 
                        alt={`${team.name} logo`} 
                        className="w-12 h-12 object-contain"
                      />
                    )}
                  </div>
                  <div>
                    <h2 className="text-xl font-bold text-gray-800">{team.name} Team</h2>
                    <p className="text-sm text-gray-600">
                      {totalMembers} member{totalMembers !== 1 ? 's' : ''}
                      {team.ceo && ` • Led by ${team.ceo.name} (CEO)`}
                      {!team.ceo && team.cro && ` • Led by ${team.cro.name} (CRO)`}
                      {!team.ceo && !team.cro && team.sdm && ` • Led by ${team.sdm.name} (SDM)`}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="bg-white bg-opacity-70 px-3 py-1 rounded-full text-sm font-medium">
                    {totalMembers}
                  </span>
                  {isExpanded ? (
                    <ChevronUp className="w-5 h-5 text-gray-600" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-600" />
                  )}
                </div>
              </div>
            </div>

            {/* Team Content */}
            {isExpanded && (
              <div className="px-6 pb-6 space-y-6">
                {/* Leadership Section */}
                {(team.ceo || team.cro || team.sdm || team.tdm || team.cxm) && (
                  <div>
                    <h3 className="text-sm font-semibold text-gray-700 mb-3 uppercase tracking-wide">
                      Leadership Team
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {team.ceo && renderLeadershipCard(
                        team.ceo,
                        'Chief Executive Officer',
                        <Crown className="w-3 h-3 text-white" />
                      )}
                      {team.cro && renderLeadershipCard(
                        team.cro,
                        'Chief Revenue Officer',
                        <Crown className="w-3 h-3 text-white" />
                      )}
                      {team.sdm && renderLeadershipCard(
                        team.sdm,
                        'Service Delivery Manager',
                        <Shield className="w-3 h-3 text-white" />
                      )}
                      {team.tdm && renderLeadershipCard(
                        team.tdm,
                        'Technical Delivery Manager',
                        <Shield className="w-3 h-3 text-white" />
                      )}
                      {team.cxm && renderLeadershipCard(
                        team.cxm,
                        'Client Experience Manager',
                        <Star className="w-3 h-3 text-white" />
                      )}
                    </div>
                  </div>
                )}

                {/* Team Members Section */}
                {team.members.length > 0 && (
                  <div>
                    <h3 className="text-sm font-semibold text-gray-700 mb-3 uppercase tracking-wide">
                      Team Members ({team.members.length})
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                      {team.members
                        .sort((a, b) => {
                          // Sort active members first
                          if (a.status === 'active' && b.status !== 'active') return -1;
                          if (a.status !== 'active' && b.status === 'active') return 1;
                          return a.name.localeCompare(b.name);
                        })
                        .map(renderMemberCard)}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default EnhancedTeamView;

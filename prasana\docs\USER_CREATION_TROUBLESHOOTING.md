# User Creation Troubleshooting Guide

## Problem: "Failed to create user: Database error creating new user"

This error typically occurs when trying to create users in Supabase authentication. Here are the most common causes and solutions:

## 1. **Check Supabase Authentication Settings**

### Via Supabase Dashboard:
1. Go to your Supabase project dashboard
2. Navigate to **Authentication** → **Settings**
3. Check the following settings:

#### Enable Signup
- Ensure "Enable signup" is **enabled**
- If disabled, users cannot register new accounts

#### Email Confirmation
- Check if "Enable email confirmations" is enabled
- If enabled, users must confirm their email before the account is active
- For testing, you can disable this temporarily

#### Site URL
- Verify your site URL is correctly configured
- Should match your application's URL (e.g., `http://localhost:5173` for development)

#### Email Templates
- Ensure email templates are properly configured if email confirmation is enabled

## 2. **Common Error Messages and Solutions**

### "Signup is disabled"
**Solution:** Enable signup in Authentication Settings

### "Invalid email domain"
**Solution:** Check if your email domain is blocked or if there are domain restrictions

### "Password too weak"
**Solution:** Ensure password meets minimum requirements (usually 6+ characters)

### "User already registered"
**Solution:** The email is already in use. Try a different email or use the login function instead

### "Email confirmation required"
**Solution:** This is not an error - check the user's email for a confirmation link

## 3. **Testing User Creation**

### Method 1: Use the Built-in Testing Components
1. Login as admin in your application
2. Navigate to "User Testing" in the sidebar
3. Use the "Test User Registration" component to test signup functionality
4. Use the "Admin User Creator" for administrative user creation

### Method 2: Manual Testing via Supabase Dashboard
1. Go to Authentication → Users in your Supabase dashboard
2. Click "Add user" to manually create a user
3. Fill in email and password
4. Check if the user is created successfully

### Method 3: Test via Code
```javascript
import { supabase } from './supabaseClient';

const testUserCreation = async () => {
  const { data, error } = await supabase.auth.signUp({
    email: '<EMAIL>',
    password: 'password123',
    options: {
      data: {
        full_name: 'Test User',
      }
    }
  });
  
  console.log('Result:', { data, error });
};
```

## 4. **Database Trigger Issues**

Your application has a trigger that automatically creates a user record when a new auth user is created:

```sql
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO users (id, email, full_name, created_at)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        NEW.created_at
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### Potential Issues:
1. **RLS Policies:** Row Level Security might be blocking the trigger
2. **Missing Permissions:** The trigger function might not have proper permissions
3. **Table Structure:** The `users` table structure might not match the trigger

### Solutions:
1. Check RLS policies on the `users` table
2. Ensure the trigger function has `SECURITY DEFINER` (which it does)
3. Verify the `users` table exists and has the correct columns

## 5. **Row Level Security (RLS) Issues**

If RLS is enabled on your tables, it might be blocking user creation.

### Check RLS Status:
```sql
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'users';
```

### Temporarily Disable RLS for Testing:
```sql
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
```

### Re-enable RLS with Proper Policies:
```sql
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Allow users to read their own data
CREATE POLICY "Users can view own profile" ON users
FOR SELECT USING (auth.uid() = id);

-- Allow users to update their own data
CREATE POLICY "Users can update own profile" ON users
FOR UPDATE USING (auth.uid() = id);

-- Allow the trigger to insert new users
CREATE POLICY "Enable insert for authenticated users only" ON users
FOR INSERT WITH CHECK (true);
```

## 6. **Client-Side Implementation**

### Using the New Signup Component:
```jsx
import UserSignup from './components/UserSignup';

// In your component
<UserSignup 
  onSignupSuccess={() => console.log('User created!')}
  onClose={() => setShowSignup(false)}
/>
```

### Using the Auth Modal:
```jsx
import AuthModal from './components/AuthModal';

// In your component
<AuthModal
  isOpen={showAuth}
  onClose={() => setShowAuth(false)}
  onLoginSuccess={() => setShowAuth(false)}
  defaultMode="signup"
/>
```

## 7. **Environment Variables**

Ensure your Supabase configuration is correct:

```typescript
// supabaseClient.ts
const supabaseUrl = 'https://your-project.supabase.co';
const supabaseAnonKey = 'your-anon-key';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);
```

## 8. **Network and CORS Issues**

### Check Browser Console:
- Look for CORS errors
- Check network requests in Developer Tools
- Verify API calls are reaching Supabase

### Common CORS Solutions:
- Ensure your domain is added to Supabase's allowed origins
- Check if you're using the correct Supabase URL

## 9. **Testing Checklist**

- [ ] Signup is enabled in Supabase Auth settings
- [ ] Email confirmation settings are appropriate for your use case
- [ ] Site URL is correctly configured
- [ ] RLS policies allow user creation
- [ ] Database trigger is working correctly
- [ ] No CORS or network issues
- [ ] Client-side code is using correct Supabase configuration
- [ ] Test with the provided testing components

## 10. **Getting More Information**

### Enable Detailed Logging:
```javascript
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'password123'
});

console.log('Signup Data:', data);
console.log('Signup Error:', error);

if (error) {
  console.log('Error Code:', error.status);
  console.log('Error Message:', error.message);
  console.log('Error Details:', error);
}
```

### Check Supabase Logs:
1. Go to your Supabase dashboard
2. Navigate to **Logs** → **Auth Logs**
3. Look for recent authentication attempts and errors

## Need Help?

If you're still experiencing issues:
1. Use the testing components in your application
2. Check the browser console for detailed error messages
3. Review Supabase Auth logs in the dashboard
4. Verify your database schema and RLS policies
5. Test with a simple email/password combination first

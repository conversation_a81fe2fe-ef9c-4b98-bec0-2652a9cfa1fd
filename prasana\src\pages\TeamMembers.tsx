import React, { useState, useEffect } from 'react';
import ProfileAvatar from '../components/ProfileAvatar';
import BadgeManagement from '../components/BadgeManagement';
import BadgeImage from '../components/BadgeImage';
import { Search, Filter, Award } from 'lucide-react';
import { useUser } from '../contexts/UserContext';
import { fetchEmployeeBadges } from '../data/supabaseBadges';
import { fetchTeamMembers } from '../data/supabaseTeams';
import { TeamMember } from '../types/team';

// This function is no longer needed as we use real UUIDs from database

const TeamMembers: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTeam, setSelectedTeam] = useState<string | null>(null);
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [selectedMember, setSelectedMember] = useState<any | null>(null);
  const [showBadgeModal, setShowBadgeModal] = useState(false);
  const { isAdmin } = useUser();
  const [memberBadges, setMemberBadges] = useState<Record<string, any[]>>({});
  
  useEffect(() => {
    loadAllMemberBadges();
  }, []);

  // State for team members from database
  const [allMembers, setAllMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);

  // Load team members from database
  useEffect(() => {
    const loadTeamMembers = async () => {
      try {
        setLoading(true);
        const members = await fetchTeamMembers();
        setAllMembers(members);

        // Load badges for all members after getting the real member data
        await loadAllMemberBadges(members);
      } catch (error) {
        console.error('Error loading team members:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTeamMembers();
  }, []);

  // Load badges for all members
  const loadAllMemberBadges = async (members: TeamMember[] = allMembers) => {
    try {
      const badgePromises = members.map(async (member) => {
        if (!member.id) {
          return { memberId: '', badges: [] };
        }

        try {
          const badges = await fetchEmployeeBadges(member.id);
          return { memberId: member.id, badges };
        } catch (error) {
          return { memberId: member.id, badges: [] };
        }
      });

      const results = await Promise.all(badgePromises);
      const badgeMap: Record<string, any[]> = {};
      results.forEach(({ memberId, badges }) => {
        if (memberId && badges && Array.isArray(badges)) {
          badgeMap[memberId] = badges;
        } else {
          badgeMap[memberId] = [];
        }
      });
      setMemberBadges(badgeMap);
    } catch (error) {
      console.error('Error loading member badges:', error);
    }
  };

  // Calculate days remaining for a badge
  const calculateDaysRemaining = (expiryDate: string): number => {
    if (!expiryDate) return -1; // No expiry
    const expiry = new Date(expiryDate);
    const now = new Date();
    const diffTime = expiry.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // Filter members based on search and filters
  const filteredMembers = allMembers.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         member.role.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (member.employeeCode && member.employeeCode.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesTeam = !selectedTeam || member.team === selectedTeam;
    const matchesRole = !selectedRole || member.role === selectedRole;
    const matchesStatus = !selectedStatus || member.status === selectedStatus;
    return matchesSearch && matchesTeam && matchesRole && matchesStatus;
  });

  // Get unique roles and statuses for filters
  const uniqueRoles = Array.from(new Set(allMembers.map(m => m.role)));
  const uniqueStatuses = Array.from(new Set(allMembers.map(m => m.status)));

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-6 sm:py-8">
          <div className="mb-6 sm:mb-8">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">Team Members</h1>
            <p className="text-gray-600 text-sm sm:text-base">Loading team members...</p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
            {[1, 2, 3, 4, 5, 6, 7, 8].map(i => (
              <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden animate-pulse">
                <div className="p-4 sm:p-5">
                  <div className="flex items-start space-x-3 sm:space-x-4">
                    <div className="w-12 h-12 bg-gray-200 rounded-full flex-shrink-0"></div>
                    <div className="flex-1 min-w-0">
                      <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-32 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-16 mb-3"></div>
                      <div className="flex gap-2">
                        <div className="h-6 bg-gray-200 rounded-full w-16"></div>
                        <div className="h-6 bg-gray-200 rounded-full w-12"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-6 sm:py-8">
        {/* Header Section */}
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">Team Members</h1>
          <p className="text-gray-600 text-sm sm:text-base mb-4">View all employees and their achievements across different teams and departments</p>

          {/* Info badges - responsive layout */}
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 text-sm">
            <span className="flex items-center gap-1 text-green-600 bg-green-50 px-3 py-2 rounded-full w-fit">
              <Award size={14} />
              <span className="hidden sm:inline">All users can view member badges and achievements</span>
              <span className="sm:hidden">View badges & achievements</span>
            </span>
            {isAdmin && (
              <span className="flex items-center gap-1 text-blue-600 bg-blue-50 px-3 py-2 rounded-full w-fit">
                <Award size={14} />
                <span className="hidden sm:inline">Admin: Can manage badges</span>
                <span className="sm:hidden">Admin: Manage badges</span>
              </span>
            )}
          </div>
        </div>

        {/* Search and Filters - Improved Mobile Layout */}
        <div className="mb-6 space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <input
              type="text"
              placeholder="Search by name, role, or PS Number..."
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {/* Filters - Mobile responsive grid */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
            <select
              className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm"
              value={selectedTeam || ''}
              onChange={(e) => setSelectedTeam(e.target.value || null)}
            >
              <option value="">All Teams</option>
              <option value="TITAN">TITAN</option>
              <option value="NEXUS">NEXUS</option>
              <option value="ATHENA">ATHENA</option>
              <option value="DYNAMIX">DYNAMIX</option>
            </select>
            <select
              className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm"
              value={selectedRole || ''}
              onChange={(e) => setSelectedRole(e.target.value || null)}
            >
              <option value="">All Roles</option>
              {uniqueRoles.map(role => (
                <option key={role} value={role}>{role}</option>
              ))}
            </select>
            <select
              className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm"
              value={selectedStatus || ''}
              onChange={(e) => setSelectedStatus(e.target.value || null)}
            >
              <option value="">All Status</option>
              {uniqueStatuses.map(status => (
                <option key={status} value={status}>
                  {status === 'active' ? 'Active' : status === 'terminated' ? 'Terminated' : status}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Team Members Grid - Responsive */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
          {filteredMembers.map((member, index) => (
            <div
              key={index}
              className={`bg-white rounded-xl shadow-sm border hover:shadow-lg transition-all duration-200 overflow-hidden ${
                member.isLeadership ? 'border-blue-200 ring-1 ring-blue-100' : 'border-gray-200'
              }`}
            >
              {/* Card Header */}
              <div className="p-4 sm:p-5">
                <div className="flex items-start space-x-3 sm:space-x-4">
                  <div className="flex-shrink-0">
                    <ProfileAvatar
                      name={member.name}
                      avatar={member.avatar}
                      size="lg"
                      team={member.team as any}
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-1">
                      <div className="min-w-0 flex-1">
                        <h3 className="font-semibold text-gray-900 truncate text-sm sm:text-base">{member.name}</h3>
                        <p className="text-xs sm:text-sm text-gray-600 truncate">{member.role}</p>
                      </div>
                      {/* Quick Badge Count Indicator */}
                      {memberBadges[member.id]?.length > 0 && (
                        <div className="flex items-center gap-1 bg-blue-100 text-blue-800 px-2 py-1 rounded-full flex-shrink-0 ml-2">
                          <Award size={10} />
                          <span className="text-xs font-medium">{memberBadges[member.id].length}</span>
                        </div>
                      )}
                    </div>

                    {/* Employee Details */}
                    <div className="space-y-1">
                      {member.employeeCode && (
                        <p className="text-xs text-blue-600 font-medium">PS: {member.employeeCode}</p>
                      )}
                      {member.designation && (
                        <p className="text-xs text-gray-500 truncate">{member.designation}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Status Tags */}
                <div className="mt-3 flex flex-wrap gap-1.5">
                  <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${
                    member.team === 'TITAN' ? 'bg-blue-100 text-blue-800' :
                    member.team === 'NEXUS' ? 'bg-purple-100 text-purple-800' :
                    member.team === 'ATHENA' ? 'bg-green-100 text-green-800' :
                    'bg-amber-100 text-amber-800'
                  }`}>
                    {member.team}
                  </span>
                  {member.isLeadership && (
                    <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                      Leadership
                    </span>
                  )}
                  <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${
                    member.status === 'active' ? 'bg-green-100 text-green-800' :
                    member.status === 'terminated' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {member.status === 'active' ? 'Active' :
                     member.status === 'terminated' ? 'Terminated' :
                     member.status}
                  </span>
                </div>
              </div>
                
              {/* Badges Section - Only show if user has badges */}
              {memberBadges[member.id]?.length > 0 && (
                <div className="border-t border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50 p-3 sm:p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <Award size={14} className="text-blue-600" />
                      <span className="text-xs sm:text-sm font-medium text-gray-700">Achievements</span>
                    </div>
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium">
                      {memberBadges[member.id]?.length || 0} Badge{(memberBadges[member.id]?.length || 0) !== 1 ? 's' : ''}
                    </span>
                  </div>

                  <div className="flex flex-wrap gap-2 sm:gap-3">
                    {memberBadges[member.id].slice(0, 4).map((badge: any) => {
                      const daysRemaining = badge.expiry_date ? calculateDaysRemaining(badge.expiry_date) : -1;
                      return (
                        <div key={badge.id} className="relative group">
                          <BadgeImage
                            badge={badge}
                            size="sm"
                            daysRemaining={daysRemaining}
                            awardedDate={badge.assigned_date}
                          />
                          {/* Enhanced tooltip for all users */}
                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-20">
                            <div className="font-medium">{badge.badge_type?.name || badge.name}</div>
                            <div className="text-gray-300">Awarded: {new Date(badge.assigned_date).toLocaleDateString()}</div>
                            {badge.expiry_date && (
                              <div className="text-gray-300">
                                Expires: {new Date(badge.expiry_date).toLocaleDateString()}
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                    {memberBadges[member.id].length > 4 && (
                      <div className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 bg-gray-200 rounded-lg text-xs font-medium text-gray-600">
                        +{memberBadges[member.id].length - 4}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Manage Badges Button (Admin Only) */}
              {isAdmin && (
                <div className="border-t border-gray-100 p-3 sm:p-4">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedMember(member);
                      setShowBadgeModal(true);
                    }}
                    className="w-full text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 flex items-center justify-center gap-2 py-2 px-3 rounded-lg transition-colors duration-200"
                  >
                    <Award size={16} />
                    Manage Badges
                  </button>
                </div>
              )}
              </div>
            </div>
          </div>
        ))}
      </div>

        {/* Empty State */}
        {filteredMembers.length === 0 && (
          <div className="text-center py-12 sm:py-16">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 sm:p-12 max-w-md mx-auto">
              <Filter className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No members found</h3>
              <p className="text-sm text-gray-500">
                Try adjusting your search or filter criteria to find team members
              </p>
            </div>
          </div>
        )}

        {/* Badge Management Modal (Admin Only) */}
      {isAdmin && showBadgeModal && selectedMember && (
        <BadgeManagement
          userId={selectedMember.id}
          userName={selectedMember.name}
          onClose={() => {
            setShowBadgeModal(false);
            loadAllMemberBadges(allMembers); // Refresh badges after modal closes
          }}
        />
      )}
    </div>
  );
};

export default TeamMembers; 
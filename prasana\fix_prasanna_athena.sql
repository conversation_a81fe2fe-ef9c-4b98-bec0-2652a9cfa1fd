-- Fix Prasanna team assignment to ATHENA
-- Run this SQL in your Supabase SQL Editor

-- Step 1: Check current state
SELECT 
    e.id,
    e.first_name,
    e.last_name,
    e.email,
    e.department,
    t.name as team_name
FROM employees e
LEFT JOIN teams t ON e.team_id = t.id
WHERE e.first_name ILIKE '%prasanna%'
   OR e.email ILIKE '%prasanna%';

-- Step 2: Get ATHENA team ID
WITH athena_team AS (
    SELECT id as athena_id FROM teams WHERE name = 'ATHENA'
)
-- Step 3: Update Prasanna to ATHENA team
UPDATE employees 
SET 
    team_id = (SELECT athena_id FROM athena_team),
    department = 'ATHENA',
    updated_at = NOW()
WHERE (first_name ILIKE '%prasanna%' OR email ILIKE '%prasanna%')
  AND status = 'active';

-- Step 4: Verify the change
SELECT 
    e.id,
    e.first_name,
    e.last_name,
    e.email,
    e.department,
    t.name as team_name
FROM employees e
LEFT JOIN teams t ON e.team_id = t.id
WHERE e.first_name ILIKE '%prasanna%'
   OR e.email ILIKE '%prasanna%';

-- Step 5: Check all ATHENA team members
SELECT 
    e.first_name,
    e.last_name,
    e.designation,
    e.department,
    t.name as team_name
FROM employees e
LEFT JOIN teams t ON e.team_id = t.id
WHERE t.name = 'ATHENA'
  AND e.status = 'active'
ORDER BY e.first_name;

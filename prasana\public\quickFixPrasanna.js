// Quick fix for <PERSON><PERSON><PERSON> team assignment - Run in browser console
// This will immediately move <PERSON><PERSON><PERSON> to ATHENA team

async function quickFixPrasanna() {
  console.log('🔧 Quick fixing Prasanna team assignment...');
  
  try {
    // Import supabase client
    const { supabase } = await import('/src/supabaseClient.js');
    
    if (!supabase) {
      throw new Error('Supabase client not available');
    }

    // Step 1: Find ATHENA team
    console.log('🔍 Finding ATHENA team...');
    const { data: athenaTeam, error: athenaError } = await supabase
      .from('teams')
      .select('id, name')
      .eq('name', 'ATHENA')
      .single();

    if (athenaError) {
      console.error('❌ Error finding ATHENA team:', athenaError);
      throw athenaError;
    }

    console.log('✅ Found ATHENA team:', athenaTeam);

    // Step 2: Find Prasanna
    console.log('🔍 Finding Prasanna...');
    const { data: prasannaList, error: prasannaError } = await supabase
      .from('employees')
      .select(`
        id, 
        first_name, 
        last_name, 
        email, 
        team_id,
        department,
        teams(name)
      `)
      .ilike('first_name', '%prasanna%');

    if (prasannaError) {
      console.error('❌ Error finding Prasanna:', prasannaError);
      throw prasannaError;
    }

    console.log('🔍 Found employees with Prasanna in name:', prasannaList);

    // Find the right Prasanna
    const prasanna = prasannaList.find(emp => 
      emp.first_name.toLowerCase().includes('prasanna') || 
      emp.email?.includes('prasanna')
    );

    if (!prasanna) {
      throw new Error('Prasanna not found in database');
    }

    console.log('✅ Found Prasanna:', {
      id: prasanna.id,
      name: `${prasanna.first_name} ${prasanna.last_name}`,
      email: prasanna.email,
      currentTeam: prasanna.teams?.name,
      currentDepartment: prasanna.department
    });

    // Step 3: Update Prasanna's team
    console.log('🔄 Moving Prasanna to ATHENA team...');
    const { error: updateError } = await supabase
      .from('employees')
      .update({
        team_id: athenaTeam.id,
        department: 'ATHENA'
      })
      .eq('id', prasanna.id);

    if (updateError) {
      console.error('❌ Error updating Prasanna:', updateError);
      throw updateError;
    }

    // Step 4: Verify the update
    console.log('✅ Verifying update...');
    const { data: updatedPrasanna, error: verifyError } = await supabase
      .from('employees')
      .select(`
        first_name, 
        last_name, 
        department,
        teams(name)
      `)
      .eq('id', prasanna.id)
      .single();

    if (verifyError) {
      console.warn('⚠️ Could not verify update:', verifyError);
    } else {
      console.log('✅ Update verified:', {
        name: `${updatedPrasanna.first_name} ${updatedPrasanna.last_name}`,
        team: updatedPrasanna.teams?.name,
        department: updatedPrasanna.department
      });
    }

    console.log('🎉 SUCCESS! Prasanna has been moved to ATHENA team');
    console.log('📝 Please refresh the Team Collaboration page to see the changes');

    return {
      success: true,
      message: 'Prasanna successfully moved to ATHENA team',
      previousTeam: prasanna.teams?.name,
      newTeam: 'ATHENA'
    };

  } catch (error) {
    console.error('❌ Quick fix failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Auto-load message
console.log('🚀 Quick Fix Prasanna Script Loaded');
console.log('📋 Run: quickFixPrasanna()');
console.log('🔄 Then refresh the Team Collaboration page');

// Make function globally available
window.quickFixPrasanna = quickFixPrasanna;

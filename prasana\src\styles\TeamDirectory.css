/* Team Directory Styles */

/* Organizational Chart Styles */
.org-chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
  scroll-behavior: smooth;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.org-chart-node {
  transition: all 0.3s ease;
  position: relative;
}

.org-chart-node:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Prevent layout shifts during expansion */
.org-chart-level {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  position: relative;
}

.org-chart-children-container {
  display: grid;
  gap: 2rem;
  justify-items: center;
  width: 100%;
  margin-top: 1.5rem;
  transition: all 0.3s ease;
}

.org-chart-children-container.level-1 {
  grid-template-columns: 1fr;
}

.org-chart-children-container.level-2 {
  grid-template-columns: 1fr;
}

.org-chart-children-container.level-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  max-width: 1200px;
}

.org-chart-children-container.level-4 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  max-width: 800px;
}

.org-chart-children-container.level-5 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  max-width: 600px;
}

/* Node positioning */
.org-chart-node-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: fit-content;
  width: 100%;
}

/* Smooth expansion animations */
.org-chart-children-container {
  animation: expandIn 0.3s ease-out;
}

@keyframes expandIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Prevent horizontal scrolling issues */
.org-chart-container {
  contain: layout style;
}

/* Dynamic layout for SDMs to prevent overlap */
.org-chart-sdm-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
  gap: 1.5rem;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  transition: all 0.3s ease;
}

.org-chart-sdm-container.vertical-layout {
  flex-direction: column;
  gap: 6rem;
  align-items: center;
  max-width: 1000px;
}

.org-chart-sdm-item {
  flex: 1 1 280px;
  max-width: 350px;
  min-width: 250px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  margin-bottom: 3rem;
  padding: 1rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.org-chart-sdm-item.full-width {
  flex: 1 1 100%;
  max-width: 900px;
  margin-bottom: 4rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Stable grid layouts for other levels */
.org-chart-children-container.level-3 {
  display: grid;
  grid-template-columns: repeat(4, minmax(250px, 1fr));
  gap: 2rem;
  justify-items: center;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

.org-chart-children-container.level-4 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
  gap: 3rem;
  max-width: 1000px;
  margin: 4rem auto;
  padding: 3rem 2rem;
  background: rgba(248, 250, 252, 0.9);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.org-chart-children-container.level-5 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
  gap: 2rem;
  max-width: 800px;
  margin: 3rem auto;
  padding: 2rem 1rem;
  background: rgba(241, 245, 249, 0.9);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
}

/* Add spacing between expanded sections */
.org-chart-level {
  margin-bottom: 8rem;
  position: relative;
  z-index: 1;
}

/* Prevent overlap in vertical layout */
.org-chart-sdm-container.vertical-layout .org-chart-sdm-item {
  margin-bottom: 4rem;
  padding: 2rem;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
}

/* Ensure children containers don't overlap */
.org-chart-children-container {
  position: relative;
  z-index: 3;
  clear: both;
  overflow: visible;
}

/* Force minimum spacing between all elements */
.org-chart-node {
  margin: 1rem;
  position: relative;
  z-index: 4;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

/* Ensure all employee cards have consistent sizing */
.employee-card {
  width: 280px;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: 0 auto;
}

/* Connection lines */
.org-chart-connection-line {
  position: absolute;
  background-color: #d1d5db;
  transition: all 0.3s ease;
}

/* Hover effects for better UX */
.org-chart-node:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 20;
}

/* Prevent text selection during drag */
.org-chart-container * {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Clear visual separation for expanded sections */
.org-chart-sdm-item::before {
  content: '';
  position: absolute;
  top: -2rem;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
  border-radius: 1px;
}

.org-chart-sdm-item.full-width::before {
  width: 90%;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
}

/* Staggered animation for vertical layout */
.org-chart-sdm-container.vertical-layout .org-chart-sdm-item:nth-child(1) {
  animation-delay: 0.1s;
}

.org-chart-sdm-container.vertical-layout .org-chart-sdm-item:nth-child(2) {
  animation-delay: 0.2s;
}

.org-chart-sdm-container.vertical-layout .org-chart-sdm-item:nth-child(3) {
  animation-delay: 0.3s;
}

.org-chart-sdm-container.vertical-layout .org-chart-sdm-item:nth-child(4) {
  animation-delay: 0.4s;
}

.org-chart-connection {
  stroke: #d1d5db;
  stroke-width: 2;
  fill: none;
}

.org-chart-connection-highlight {
  stroke: #3b82f6;
  stroke-width: 3;
}

/* Employee Card Animations */
.employee-card {
  transition: all 0.2s ease;
}

.employee-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12);
}

.employee-card .avatar-container {
  position: relative;
  overflow: hidden;
}

.employee-card .avatar-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.employee-card:hover .avatar-container::before {
  transform: translateX(100%);
}

/* Search and Filter Animations */
.filter-chip {
  transition: all 0.2s ease;
  cursor: pointer;
}

.filter-chip:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.filter-chip.active {
  animation: chipPulse 0.3s ease;
}

@keyframes chipPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Progress Bar Animations */
.progress-bar {
  overflow: hidden;
  background-color: #e5e7eb;
  border-radius: 9999px;
}

.progress-fill {
  height: 100%;
  border-radius: 9999px;
  transition: width 0.8s ease-in-out;
  position: relative;
  overflow: hidden;
}

.progress-fill::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Modal Animations */
.modal-backdrop {
  animation: fadeIn 0.2s ease;
}

.modal-content {
  animation: slideUp 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Badge Styles */
.badge-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.badge:hover {
  transform: scale(1.05);
}

/* Department Color Variants */
.dept-nexus {
  background-color: #dbeafe;
  color: #1e40af;
  border: 1px solid #93c5fd;
}

.dept-dynamix {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #6ee7b7;
}

.dept-titan {
  background-color: #fed7aa;
  color: #9a3412;
  border: 1px solid #fdba74;
}

.dept-athena {
  background-color: #e9d5ff;
  color: #6b21a8;
  border: 1px solid #c4b5fd;
}

/* Loading Animations */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .org-chart-node {
    min-width: 180px;
    max-width: 200px;
  }
  
  .employee-card {
    margin-bottom: 1rem;
  }
  
  .modal-content {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }
}

/* Print Styles */
@media print {
  .org-chart-container {
    overflow: visible;
    height: auto;
  }
  
  .employee-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }
  
  .modal-backdrop,
  .filter-controls,
  .action-buttons {
    display: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .employee-card {
    border: 2px solid #000;
  }
  
  .org-chart-connection {
    stroke: #000;
    stroke-width: 3;
  }
  
  .badge {
    border: 2px solid currentColor;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .employee-card,
  .org-chart-node,
  .filter-chip,
  .progress-fill,
  .badge {
    transition: none;
    animation: none;
  }
  
  .progress-fill::before {
    animation: none;
  }
}

/* Focus Styles for Accessibility */
.employee-card:focus,
.org-chart-node:focus,
.filter-chip:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Custom Scrollbar */
.team-directory-scroll::-webkit-scrollbar {
  width: 8px;
}

.team-directory-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.team-directory-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.team-directory-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Tooltip Styles */
.tooltip {
  position: relative;
}

.tooltip::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: white;
  padding: 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 1000;
}

.tooltip:hover::before {
  opacity: 1;
}

/* Status Indicators */
.status-active {
  background-color: #10b981;
}

.status-inactive {
  background-color: #6b7280;
}

.status-on-leave {
  background-color: #f59e0b;
}

/* Leadership Indicators */
.leadership-badge {
  background: linear-gradient(45deg, #fbbf24, #f59e0b);
  color: #92400e;
  box-shadow: 0 2px 4px rgba(251, 191, 36, 0.3);
}

/* Department Specific Styles */
.nexus-theme {
  --primary-color: #3b82f6;
  --primary-light: #dbeafe;
  --primary-dark: #1e40af;
}

.dynamix-theme {
  --primary-color: #10b981;
  --primary-light: #d1fae5;
  --primary-dark: #065f46;
}

.titan-theme {
  --primary-color: #f59e0b;
  --primary-light: #fed7aa;
  --primary-dark: #9a3412;
}

.athena-theme {
  --primary-color: #8b5cf6;
  --primary-light: #e9d5ff;
  --primary-dark: #6b21a8;
}

import React, { useState, useEffect } from 'react';
import {
  FileText,
  Download,
  Calendar,
  Filter,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  Users,
  DollarSign,
  Eye,
  Printer
} from 'lucide-react';
import { PayrollPeriod, PayrollReport } from '../../types/payroll';
import { payrollService } from '../../services/payrollService';
import { formatINR, formatEmployeeName, formatDepartment } from '../../utils/currency';
import { supabase } from '../../supabaseClient';

const PayrollReports: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<string>('');
  const [reportType, setReportType] = useState<string>('salary_register');
  const [periods, setPeriods] = useState<PayrollPeriod[]>([]);
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState<any[]>([]);

  useEffect(() => {
    loadPayrollPeriods();
  }, []);

  const loadPayrollPeriods = async () => {
    try {
      const periodsData = await payrollService.getPayrollPeriods();
      setPeriods(periodsData);
      if (periodsData.length > 0) {
        setSelectedPeriod(periodsData[0].id);
      }
    } catch (error) {
      console.error('Error loading periods:', error);
    }
  };

  const generateReport = async () => {
    if (!selectedPeriod) return;

    setLoading(true);
    try {
      // Generate real report data based on selected period and type
      let reportData = [];

      if (reportType === 'salary_register') {
        // Get employees with their salary structures
        const { data: employees, error: empError } = await supabase
          .from('employees')
          .select(`
            *,
            employee_salary_structure!inner(*)
          `)
          .eq('status', 'active')
          .eq('employee_salary_structure.status', 'active');

        if (empError) throw empError;

        reportData = employees?.map(emp => {
          const structure = emp.employee_salary_structure[0];
          const allowances = structure.hra + structure.transport_allowance +
            structure.medical_allowance + structure.special_allowance + structure.other_allowances;
          const gross_salary = structure.basic_salary + allowances;
          const deductions = structure.provident_fund + structure.professional_tax +
            structure.income_tax + structure.other_deductions;
          const net_salary = gross_salary - deductions;

          return {
            employee_name: formatEmployeeName(emp.first_name, emp.last_name),
            employee_code: emp.employee_code || 'N/A',
            department: formatDepartment(emp.department),
            basic_salary: structure.basic_salary,
            allowances: allowances,
            gross_salary: gross_salary,
            deductions: deductions,
            net_salary: net_salary
          };
        }) || [];
      } else {
        // For other report types, use sample data for now
        reportData = [
          {
            employee_name: 'Sample Employee',
            employee_code: 'EMP000',
            department: 'Sample Department',
            basic_salary: 50000,
            allowances: 25000,
            gross_salary: 75000,
            deductions: 15000,
            net_salary: 60000
          }
        ];
      }

      setReportData(reportData);
    } catch (error) {
      console.error('Error generating report:', error);
      setReportData([]);
    } finally {
      setLoading(false);
    }
  };

  const exportReport = (format: 'pdf' | 'excel' | 'csv') => {
    console.log(`Exporting report as ${format}`);
    // Implementation for export functionality
  };

  const reportTypes = [
    { value: 'salary_register', label: 'Salary Register', icon: FileText },
    { value: 'tax_summary', label: 'Tax Summary', icon: BarChart3 },
    { value: 'pf_summary', label: 'PF Summary', icon: PieChart },
    { value: 'department_wise', label: 'Department-wise Report', icon: Users },
    { value: 'payroll_summary', label: 'Payroll Summary', icon: DollarSign }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-black gradient-text header-section animate-scaleIn">Payroll Reports</h1>
          <p className="text-gray-600 mt-1">Generate and export comprehensive payroll reports</p>
        </div>
      </div>

      {/* Report Configuration */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Report Configuration</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Report Type</label>
            <select
              value={reportType}
              onChange={(e) => setReportType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {reportTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Payroll Period</label>
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select Period</option>
              {periods.map((period) => (
                <option key={period.id} value={period.id}>
                  {period.period_name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={generateReport}
              disabled={!selectedPeriod || loading}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Generating...' : 'Generate Report'}
            </button>
          </div>
        </div>
      </div>

      {/* Report Actions */}
      {reportData.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Report Actions</h3>
            <div className="flex gap-2">
              <button
                onClick={() => exportReport('pdf')}
                className="flex items-center gap-2 px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm"
              >
                <Download className="h-4 w-4" />
                PDF
              </button>
              <button
                onClick={() => exportReport('excel')}
                className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
              >
                <Download className="h-4 w-4" />
                Excel
              </button>
              <button
                onClick={() => exportReport('csv')}
                className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
              >
                <Download className="h-4 w-4" />
                CSV
              </button>
              <button className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm">
                <Printer className="h-4 w-4" />
                Print
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Report Data */}
      {reportData.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              {reportTypes.find(t => t.value === reportType)?.label} Report
            </h3>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Employee
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Department
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Basic Salary
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Allowances
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Gross Salary
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Deductions
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Net Salary
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {reportData.map((row, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{row.employee_name}</div>
                        <div className="text-sm text-gray-500">{row.employee_code}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {row.department}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {formatINR(row.basic_salary, { showDecimals: false })}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatINR(row.allowances, { showDecimals: false })}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                      {formatINR(row.gross_salary, { showDecimals: false })}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                      {formatINR(row.deductions, { showDecimals: false })}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-blue-600">
                      {formatINR(row.net_salary, { showDecimals: false })}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button className="text-blue-600 hover:text-blue-900">
                        <Eye className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {/* Report Summary */}
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-sm text-gray-600">Total Employees</p>
                <p className="text-lg font-semibold text-gray-900">{reportData.length}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600">Total Gross</p>
                <p className="text-lg font-semibold text-green-600">
                  {formatINR(reportData.reduce((sum, row) => sum + row.gross_salary, 0), { showDecimals: false })}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600">Total Deductions</p>
                <p className="text-lg font-semibold text-red-600">
                  {formatINR(reportData.reduce((sum, row) => sum + row.deductions, 0), { showDecimals: false })}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600">Total Net</p>
                <p className="text-lg font-semibold text-blue-600">
                  {formatINR(reportData.reduce((sum, row) => sum + row.net_salary, 0), { showDecimals: false })}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Quick Reports */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {reportTypes.map((type) => {
          const IconComponent = type.icon;
          return (
            <div key={type.value} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <IconComponent className="h-5 w-5 text-blue-600" />
                </div>
                <h3 className="font-semibold text-gray-900">{type.label}</h3>
              </div>
              <p className="text-gray-600 text-sm mb-4">
                Generate detailed {type.label.toLowerCase()} for payroll analysis
              </p>
              <button
                onClick={() => {
                  setReportType(type.value);
                  generateReport();
                }}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
              >
                Generate Report
              </button>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default PayrollReports;

// Browser console fix for Prasanna team assignment
// Copy and paste this entire script into your browser console

(async function fixPrasannaTeamAssignment() {
    console.log('🚀 Starting Prasanna team fix...');
    
    try {
        // Get supabase client from window or import
        let supabase;
        
        // Try to get from window first
        if (window.supabase) {
            supabase = window.supabase;
        } else {
            // Try to import
            try {
                const module = await import('/src/supabaseClient.js');
                supabase = module.supabase;
            } catch (importError) {
                console.error('Could not import supabase client:', importError);
                throw new Error('Supabase client not available');
            }
        }

        console.log('✅ Supabase client loaded');

        // Step 1: Find ATHENA team
        const { data: athenaTeam, error: athenaError } = await supabase
            .from('teams')
            .select('id, name')
            .eq('name', 'ATHENA')
            .single();

        if (athenaError || !athenaTeam) {
            throw new Error('ATHENA team not found: ' + (athenaError?.message || 'No data'));
        }

        console.log('✅ Found ATHENA team:', athenaTeam);

        // Step 2: Find all employees with Prasanna in name
        const { data: prasannaEmployees, error: prasannaError } = await supabase
            .from('employees')
            .select(`
                id,
                first_name,
                last_name,
                email,
                team_id,
                department,
                status,
                teams(name)
            `)
            .or('first_name.ilike.%prasanna%,last_name.ilike.%prasanna%,email.ilike.%prasanna%');

        if (prasannaError) {
            throw new Error('Error finding Prasanna: ' + prasannaError.message);
        }

        console.log('🔍 Found employees matching Prasanna:', prasannaEmployees);

        if (!prasannaEmployees || prasannaEmployees.length === 0) {
            throw new Error('No employees found with Prasanna in name or email');
        }

        // Step 3: Update each Prasanna to ATHENA team
        for (const prasanna of prasannaEmployees) {
            console.log(`🔄 Updating ${prasanna.first_name} ${prasanna.last_name} to ATHENA team...`);
            
            const { error: updateError } = await supabase
                .from('employees')
                .update({
                    team_id: athenaTeam.id,
                    department: 'ATHENA'
                })
                .eq('id', prasanna.id);

            if (updateError) {
                console.error(`❌ Error updating ${prasanna.first_name}:`, updateError);
            } else {
                console.log(`✅ Successfully updated ${prasanna.first_name} ${prasanna.last_name}`);
            }
        }

        // Step 4: Verify ATHENA team members
        const { data: athenaMembers, error: verifyError } = await supabase
            .from('employees')
            .select(`
                first_name,
                last_name,
                designation,
                department,
                teams(name)
            `)
            .eq('team_id', athenaTeam.id)
            .eq('status', 'active')
            .order('first_name');

        if (verifyError) {
            console.error('❌ Error verifying ATHENA members:', verifyError);
        } else {
            console.log('👥 Current ATHENA team members:');
            athenaMembers.forEach(member => {
                console.log(`  - ${member.first_name} ${member.last_name} (${member.designation})`);
            });

            const prasannaInAthena = athenaMembers.find(m => 
                m.first_name.toLowerCase().includes('prasanna')
            );

            if (prasannaInAthena) {
                console.log('🎉 SUCCESS! Prasanna is now in ATHENA team');
            } else {
                console.log('❌ Prasanna still not found in ATHENA team');
            }
        }

        // Step 5: Force refresh the page data
        console.log('🔄 Refreshing page data...');
        
        // Try to trigger a page refresh or data reload
        if (window.location) {
            setTimeout(() => {
                console.log('🔄 Reloading page to show changes...');
                window.location.reload();
            }, 2000);
        }

        console.log('✅ Fix completed! Page will refresh in 2 seconds.');

        return {
            success: true,
            message: 'Prasanna team assignment fixed',
            athenaMembers: athenaMembers?.length || 0
        };

    } catch (error) {
        console.error('❌ Fix failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
})();

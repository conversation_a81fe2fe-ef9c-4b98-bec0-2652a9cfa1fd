// Fix <PERSON>'s team assignment from TITAN to ATHENA
// Run this in browser console to fix the team assignment

async function fixPrasannaTeam() {
  console.log('🔧 Starting Prasanna team fix...');
  
  try {
    // Get supabase client
    const supabase = window.supabase || (await import('/src/supabaseClient.js')).supabase;
    
    if (!supabase) {
      throw new Error('Supabase client not found');
    }

    // Step 1: Get ATHENA team ID
    const { data: athenaTeam, error: athenaError } = await supabase
      .from('teams')
      .select('id')
      .eq('name', 'ATHENA')
      .single();

    if (athenaError || !athenaTeam) {
      throw new Error('ATHENA team not found');
    }

    console.log('✅ Found ATHENA team:', athenaTeam.id);

    // Step 2: Find Prasanna
    const { data: prasanna, error: prasannaError } = await supabase
      .from('employees')
      .select('id, first_name, team_id, teams(name)')
      .eq('first_name', '<PERSON>rasanna')
      .eq('email', '<EMAIL>')
      .single();

    if (prasannaError || !prasanna) {
      throw new Error('Prasanna not found in database');
    }

    console.log('✅ Found Prasanna:', {
      id: prasanna.id,
      name: prasanna.first_name,
      currentTeam: prasanna.teams?.name
    });

    // Step 3: Update Prasanna's team
    const { error: updateError } = await supabase
      .from('employees')
      .update({
        team_id: athenaTeam.id,
        department: 'ATHENA',
        updated_at: new Date().toISOString()
      })
      .eq('id', prasanna.id);

    if (updateError) {
      throw new Error(`Failed to update Prasanna's team: ${updateError.message}`);
    }

    console.log('✅ Successfully moved Prasanna from', prasanna.teams?.name, 'to ATHENA');

    // Step 4: Verify the change
    const { data: updatedPrasanna, error: verifyError } = await supabase
      .from('employees')
      .select('first_name, teams(name)')
      .eq('id', prasanna.id)
      .single();

    if (verifyError) {
      console.warn('⚠️ Could not verify the change:', verifyError.message);
    } else {
      console.log('✅ Verification - Prasanna is now in team:', updatedPrasanna.teams?.name);
    }

    console.log('🎉 Prasanna team fix completed successfully!');
    console.log('📝 Please refresh the Team Collaboration page to see the changes');

    return {
      success: true,
      message: 'Prasanna successfully moved to ATHENA team',
      previousTeam: prasanna.teams?.name,
      newTeam: 'ATHENA'
    };

  } catch (error) {
    console.error('❌ Error fixing Prasanna team:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Auto-run if in browser console
if (typeof window !== 'undefined') {
  console.log('🚀 Prasanna Team Fix Script Loaded');
  console.log('Run fixPrasannaTeam() to move Prasanna from TITAN to ATHENA team');
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { fixPrasannaTeam };
}

import React, { useState } from 'react';
import { supabase } from '../supabaseClient';
import { UserPlus, AlertCircle, CheckCircle, Loader } from 'lucide-react';

const UserCreationTester: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<{
    type: 'success' | 'error' | 'info';
    message: string;
    details?: any;
  } | null>(null);

  const testUserCreation = async () => {
    if (!email || !password) {
      setResult({
        type: 'error',
        message: 'Please provide both email and password'
      });
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      // Test 1: Try to create user with Supabase Auth
      console.log('🔄 Attempting to create user with Supabase Auth...');
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName || email.split('@')[0],
          }
        }
      });

      console.log('📊 Signup result:', { data, error });

      if (error) {
        setResult({
          type: 'error',
          message: `Authentication Error: ${error.message}`,
          details: error
        });
        return;
      }

      if (data.user && !data.session) {
        setResult({
          type: 'info',
          message: 'User created successfully! Email confirmation required. Check your email for a confirmation link.',
          details: data
        });
        return;
      }

      if (data.user && data.session) {
        setResult({
          type: 'success',
          message: 'User created and logged in successfully!',
          details: data
        });
        return;
      }

      setResult({
        type: 'error',
        message: 'Unexpected response from Supabase',
        details: data
      });

    } catch (err) {
      console.error('❌ Exception during user creation:', err);
      setResult({
        type: 'error',
        message: `Exception: ${(err as Error).message}`,
        details: err
      });
    } finally {
      setLoading(false);
    }
  };

  const testAuthSettings = async () => {
    setLoading(true);
    setResult(null);

    try {
      // Test current auth state
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError) {
        setResult({
          type: 'error',
          message: `Auth State Error: ${authError.message}`,
          details: authError
        });
        return;
      }

      // Test database connection
      const { data: testData, error: dbError } = await supabase
        .from('users')
        .select('count')
        .limit(1);

      if (dbError) {
        setResult({
          type: 'error',
          message: `Database Connection Error: ${dbError.message}`,
          details: dbError
        });
        return;
      }

      setResult({
        type: 'success',
        message: `Auth and Database are working. Current user: ${user ? user.email : 'None'}`,
        details: { user, dbTest: testData }
      });

    } catch (err) {
      setResult({
        type: 'error',
        message: `Exception: ${(err as Error).message}`,
        details: err
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-blue-100 rounded-lg">
          <UserPlus className="w-6 h-6 text-blue-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900">User Creation Tester</h2>
      </div>

      <div className="space-y-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Email Address
          </label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="<EMAIL>"
            disabled={loading}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Password
          </label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter password (min 6 chars)"
            disabled={loading}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Full Name (Optional)
          </label>
          <input
            type="text"
            value={fullName}
            onChange={(e) => setFullName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Test User"
            disabled={loading}
          />
        </div>
      </div>

      <div className="flex space-x-4 mb-6">
        <button
          onClick={testUserCreation}
          disabled={loading}
          className="flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-blue-400"
        >
          {loading ? <Loader className="w-4 h-4 animate-spin" /> : <UserPlus className="w-4 h-4" />}
          <span>Test User Creation</span>
        </button>

        <button
          onClick={testAuthSettings}
          disabled={loading}
          className="flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:bg-gray-400"
        >
          {loading ? <Loader className="w-4 h-4 animate-spin" /> : <CheckCircle className="w-4 h-4" />}
          <span>Test Auth Settings</span>
        </button>
      </div>

      {result && (
        <div className={`p-4 rounded-lg border ${
          result.type === 'success' ? 'bg-green-50 border-green-200' :
          result.type === 'error' ? 'bg-red-50 border-red-200' :
          'bg-blue-50 border-blue-200'
        }`}>
          <div className="flex items-start space-x-3">
            {result.type === 'success' && <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />}
            {result.type === 'error' && <AlertCircle className="w-5 h-5 text-red-500 mt-0.5" />}
            {result.type === 'info' && <AlertCircle className="w-5 h-5 text-blue-500 mt-0.5" />}
            <div className="flex-1">
              <p className={`font-medium ${
                result.type === 'success' ? 'text-green-800' :
                result.type === 'error' ? 'text-red-800' :
                'text-blue-800'
              }`}>
                {result.message}
              </p>
              {result.details && (
                <details className="mt-2">
                  <summary className="cursor-pointer text-sm text-gray-600">
                    View Details
                  </summary>
                  <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                    {JSON.stringify(result.details, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserCreationTester;

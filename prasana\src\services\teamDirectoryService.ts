import { supabase } from '../supabaseClient';
import { TeamMember } from '../types/team';
import { teams } from '../data/teams';
import {
  OrganizationNode,
  TeamAnalytics,
  EmployeeProfile,
  SearchFilters,
  DEPARTMENT_COLORS,
  HIERARCHY_LEVELS,
  DESIGNATION_HIERARCHY
} from '../types/teamDirectory';

// Fetch all employees with enhanced data for team directory
export const fetchAllEmployees = async (): Promise<TeamMember[]> => {
  try {
    const { data, error } = await supabase
      .from('employees')
      .select(`
        id,
        user_id,
        employee_id,
        first_name,
        last_name,
        email,
        phone,
        designation,
        department,
        location,
        manager_id,
        joining_date,
        status,
        teams(
          id,
          name,
          description
        ),
        manager:employees!manager_id(first_name, last_name, designation)
      `)
      .eq('status', 'active')
      .order('first_name', { ascending: true });

    if (error) {
      console.error('Error fetching employees:', error);
      throw error;
    }

    return data?.map(employee => {
      const fullName = `${employee.first_name} ${employee.last_name}`.trim();
      const teamName = employee.teams?.name || employee.department;

      return {
        id: employee.id,
        name: fullName,
        role: employee.designation as any,
        designation: employee.designation,
        email: employee.email,
        phone: employee.phone,
        department: employee.department,
        team: teamName,
        location: employee.location,
        employeeCode: employee.employee_id,
        joiningDate: employee.joining_date,
        reportsTo: employee.manager_id,
        uuid: employee.user_id,
        status: employee.status as any,
        isLeadership: ['Service Delivery Manager', 'Technical Delivery Manager', 'Client Experience Manager', 'CEO'].includes(employee.designation),
        avatar: getAvatarPath(employee.first_name, employee.last_name, teamName),
        initials: getInitials(employee.first_name, employee.last_name)
      };
    }) || [];
  } catch (error) {
    console.error('Error in fetchAllEmployees:', error);
    return [];
  }
};

// Helper function to normalize names for comparison
const normalizeName = (name: string): string => {
  return name.toLowerCase().trim().replace(/[^\w\s]/g, '').replace(/\s+/g, ' ');
};

// Helper function to get avatar from teams data
const getAvatarFromTeams = (name: string, teamName: string): string => {
  const teamKey = teamName.toLowerCase() as keyof typeof teams;
  const team = teams[teamKey];

  if (!team) {
    console.log(`No team found for ${teamName}, using fallback avatar for ${name}`);
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random&color=fff`;
  }

  const normalizedName = normalizeName(name);

  // Check leadership roles first with normalized names
  if (team.sdm?.name && normalizeName(team.sdm.name) === normalizedName) {
    return team.sdm.avatar || '';
  }
  if (team.tdm?.name && normalizeName(team.tdm.name) === normalizedName) {
    return team.tdm.avatar || '';
  }
  if (team.cxm?.name && normalizeName(team.cxm.name) === normalizedName) {
    return team.cxm.avatar || '';
  }

  // Check regular members with normalized names
  const member = team.members?.find(m => m.name && normalizeName(m.name) === normalizedName);
  if (member?.avatar) {
    return member.avatar;
  }

  // Try partial matching for names that might be slightly different
  if (team.sdm?.name && (normalizeName(team.sdm.name).includes(normalizedName) || normalizedName.includes(normalizeName(team.sdm.name)))) {
    return team.sdm.avatar || '';
  }
  if (team.tdm?.name && (normalizeName(team.tdm.name).includes(normalizedName) || normalizedName.includes(normalizeName(team.tdm.name)))) {
    return team.tdm.avatar || '';
  }
  if (team.cxm?.name && (normalizeName(team.cxm.name).includes(normalizedName) || normalizedName.includes(normalizeName(team.cxm.name)))) {
    return team.cxm.avatar || '';
  }

  // Fallback to generated avatar
  console.log(`No avatar match found for ${name} in team ${teamName}, using fallback`);
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random&color=fff`;
};

// Helper function to get avatar path with teams integration
const getAvatarPath = (firstName: string, lastName: string, department: string): string => {
  const fullName = `${firstName} ${lastName}`.trim();

  // First try to get from teams data
  const teamAvatar = getAvatarFromTeams(fullName, department);
  if (teamAvatar && !teamAvatar.includes('ui-avatars.com')) {
    return teamAvatar;
  }

  // Fallback to conventional file naming
  const normalizedName = fullName.toLowerCase().replace(/\s+/g, '_');
  return `/profiles/${normalizedName}.png`;
};

// Helper function to get initials
const getInitials = (firstName: string, lastName: string): string => {
  return `${firstName.charAt(0)}${lastName ? lastName.charAt(0) : ''}`.toUpperCase();
};

// Build organizational chart from employee data
export const buildOrganizationChart = async (): Promise<OrganizationNode[]> => {
  try {
    // Fetch employees with manager information
    const { data, error } = await supabase
      .from('employees')
      .select(`
        id,
        user_id,
        employee_id,
        first_name,
        last_name,
        email,
        phone,
        designation,
        department,
        location,
        manager_id,
        joining_date,
        status
      `)
      .eq('status', 'active')
      .order('first_name', { ascending: true });

    if (error) {
      console.error('Error fetching employees for org chart:', error);
      throw error;
    }

    const employees = data?.map(employee => ({
      id: employee.id,
      name: `${employee.first_name} ${employee.last_name}`.trim(),
      role: employee.designation as any,
      designation: employee.designation,
      email: employee.email,
      phone: employee.phone,
      department: employee.department,
      team: employee.department,
      location: employee.location,
      employeeCode: employee.employee_id,
      joiningDate: employee.joining_date,
      reportsTo: employee.manager_id,
      uuid: employee.user_id,
      status: 'active' as any,
      isLeadership: ['CEO', 'Chief Revenue Officer', 'Service Delivery Manager', 'Technical Delivery Manager', 'Client Experience Manager'].includes(employee.designation),
      avatar: getAvatarPath(employee.first_name, employee.last_name, employee.department),
      initials: getInitials(employee.first_name, employee.last_name)
    })) || [];

    const nodeMap = new Map<string, OrganizationNode>();

    // Create nodes for all employees
    employees.forEach(employee => {
      const level = getHierarchyLevel(employee.designation || '');
      const node: OrganizationNode = {
        id: employee.id || '',
        name: employee.name,
        designation: employee.designation || '',
        department: employee.department || '',
        avatar: employee.avatar,
        email: employee.email,
        phone: employee.phone,
        level,
        children: [],
        isExpanded: level <= 2, // Auto-expand CEO, CRO levels
        parentId: employee.reportsTo,
        employeeData: employee
      };
      nodeMap.set(node.id, node);
    });

    // Build hierarchy by connecting children to parents
    const rootNodes: OrganizationNode[] = [];

    nodeMap.forEach(node => {
      if (node.parentId && nodeMap.has(node.parentId)) {
        const parent = nodeMap.get(node.parentId)!;
        parent.children.push(node);
      } else {
        // Root node (CEO or nodes without parents)
        rootNodes.push(node);
      }
    });

    // Sort children by hierarchy level and name
    const sortChildren = (nodes: OrganizationNode[]) => {
      nodes.forEach(node => {
        node.children.sort((a, b) => {
          if (a.level !== b.level) return a.level - b.level;
          return a.name.localeCompare(b.name);
        });
        sortChildren(node.children);
      });
    };

    sortChildren(rootNodes);

    console.log('🌳 Built organization chart:', rootNodes);
    console.log('📊 Total nodes:', nodeMap.size);
    console.log('🔝 Root nodes:', rootNodes.length);

    // Debug hierarchy levels
    console.log('🔍 Hierarchy Debug:');
    nodeMap.forEach((node, id) => {
      console.log(`  ${node.name} (${node.designation}) - Level: ${node.level}, Parent: ${node.parentId || 'None'}`);
    });

    // Debug root node structure
    if (rootNodes.length > 0) {
      console.log('🌲 Root node structure:');
      const printNode = (node: OrganizationNode, indent = '') => {
        console.log(`${indent}${node.name} (${node.designation}) - Level: ${node.level}`);
        node.children.forEach(child => printNode(child, indent + '  '));
      };
      rootNodes.forEach(node => printNode(node));
    }

    return rootNodes;
  } catch (error) {
    console.error('Error building organization chart:', error);
    return [];
  }
};

// Get hierarchy level from designation
const getHierarchyLevel = (designation: string): number => {
  return DESIGNATION_HIERARCHY[designation as keyof typeof DESIGNATION_HIERARCHY] ?? HIERARCHY_LEVELS.TEAM;
};

// Search and filter employees
export const searchEmployees = async (filters: SearchFilters): Promise<TeamMember[]> => {
  try {
    let query = supabase
      .from('employees')
      .select(`
        id,
        user_id,
        employee_id,
        first_name,
        last_name,
        email,
        phone,
        designation,
        department,
        location,
        manager_id,
        joining_date,
        status,
        teams(
          id,
          name,
          description
        )
      `)
      .eq('status', 'active');

    // Apply filters
    if (filters.departments.length > 0) {
      query = query.in('department', filters.departments);
    }

    if (filters.designations.length > 0) {
      query = query.in('designation', filters.designations);
    }

    if (filters.locations.length > 0) {
      query = query.in('location', filters.locations);
    }

    if (filters.hireDateRange.start) {
      query = query.gte('joining_date', filters.hireDateRange.start);
    }

    if (filters.hireDateRange.end) {
      query = query.lte('joining_date', filters.hireDateRange.end);
    }

    const { data, error } = await query.order('first_name', { ascending: true });

    if (error) {
      console.error('Error searching employees:', error);
      throw error;
    }

    let results = data?.map(employee => ({
      id: employee.id,
      name: `${employee.first_name} ${employee.last_name}`.trim(),
      role: employee.designation as any,
      designation: employee.designation,
      email: employee.email,
      phone: employee.phone,
      department: employee.department,
      team: employee.teams?.name || employee.department,
      location: employee.location,
      employeeCode: employee.employee_id,
      joiningDate: employee.joining_date,
      reportsTo: employee.manager_id,
      uuid: employee.user_id,
      status: employee.status as any,
      isLeadership: ['Service Delivery Manager', 'Technical Delivery Manager', 'Client Experience Manager'].includes(employee.designation),
      avatar: getAvatarPath(employee.first_name, employee.last_name, employee.department),
      initials: getInitials(employee.first_name, employee.last_name)
    })) || [];

    // Apply text search filter
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      results = results.filter(employee =>
        employee.name.toLowerCase().includes(query) ||
        employee.email?.toLowerCase().includes(query) ||
        employee.designation?.toLowerCase().includes(query) ||
        employee.department?.toLowerCase().includes(query) ||
        employee.employeeCode?.toLowerCase().includes(query)
      );
    }

    return results;
  } catch (error) {
    console.error('Error in searchEmployees:', error);
    return [];
  }
};

// Get team analytics
export const getTeamAnalytics = async (): Promise<TeamAnalytics> => {
  try {
    const employees = await fetchAllEmployees();
    const totalEmployees = employees.length;

    // Department breakdown
    const departmentCounts = employees.reduce((acc, emp) => {
      const dept = emp.department || 'Unknown';
      acc[dept] = (acc[dept] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const departmentBreakdown = Object.entries(departmentCounts).map(([department, count]) => ({
      department,
      count,
      percentage: Math.round((count / totalEmployees) * 100),
      color: DEPARTMENT_COLORS[department as keyof typeof DEPARTMENT_COLORS] || '#6B7280'
    }));

    // Hierarchy distribution
    const hierarchyCounts = employees.reduce((acc, emp) => {
      const level = getHierarchyLevel(emp.designation || '');
      const levelName = Object.keys(HIERARCHY_LEVELS)[Object.values(HIERARCHY_LEVELS).indexOf(level)] || 'TEAM';
      acc[levelName] = (acc[levelName] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const hierarchyDistribution = Object.entries(hierarchyCounts).map(([level, count]) => ({
      level,
      count,
      percentage: Math.round((count / totalEmployees) * 100)
    }));

    // Calculate average team size (employees per manager)
    const managers = employees.filter(emp => emp.isLeadership);
    const averageTeamSize = managers.length > 0 ? Math.round(totalEmployees / managers.length) : 0;

    return {
      totalEmployees,
      departmentBreakdown,
      hierarchyDistribution,
      averageTeamSize,
      skillsDistribution: [], // TODO: Implement when skills data is available
      growthTrends: [], // TODO: Implement with historical data
      locationDistribution: [] // TODO: Implement when location data is available
    };
  } catch (error) {
    console.error('Error getting team analytics:', error);
    throw error;
  }
};

// Get employee profile with detailed information
export const getEmployeeProfile = async (employeeId: string): Promise<EmployeeProfile | null> => {
  try {
    const { data, error } = await supabase
      .from('employees')
      .select(`
        *,
        teams(
          id,
          name,
          description
        )
      `)
      .eq('id', employeeId)
      .single();

    if (error) {
      console.error('Error fetching employee profile:', error);
      throw error;
    }

    if (!data) return null;

    // Get direct reports
    const { data: directReports } = await supabase
      .from('employees')
      .select('id, first_name, last_name, designation, department')
      .eq('manager_id', employeeId)
      .eq('status', 'active');

    const profile: EmployeeProfile = {
      id: data.id,
      name: `${data.first_name} ${data.last_name}`.trim(),
      role: data.designation as any,
      designation: data.designation,
      email: data.email,
      phone: data.phone,
      department: data.department,
      team: data.teams?.name || data.department,
      location: data.location,
      employeeCode: data.employee_id,
      joiningDate: data.joining_date,
      reportsTo: data.manager_id,
      uuid: data.user_id,
      status: data.status as any,
      isLeadership: ['Service Delivery Manager', 'Technical Delivery Manager', 'Client Experience Manager'].includes(data.designation),
      avatar: getAvatarPath(data.first_name, data.last_name, data.department),
      initials: getInitials(data.first_name, data.last_name),
      directReports: directReports?.map(report => ({
        id: report.id,
        name: `${report.first_name} ${report.last_name}`.trim(),
        role: report.designation as any,
        designation: report.designation,
        department: report.department
      })) || [],
      personalInfo: {
        dateOfBirth: data.date_of_birth,
        gender: data.gender,
        maritalStatus: data.marital_status,
        nationality: data.nationality,
        personalEmail: data.personal_email
      },
      jobInfo: {
        employmentType: data.employment_type,
        probationEndDate: data.probation_end_date,
        workLocation: data.location
      },
      performance: {
        // TODO: Implement when performance data is available
      },
      leaveInfo: {
        casualLeaveBalance: data.casual_leave_balance || 0,
        sickLeaveBalance: data.sick_leave_balance || 0,
        annualLeaveBalance: data.annual_leave_balance || 0,
        totalLeaveTaken: 0 // TODO: Calculate from leave applications
      },
      documents: [] // TODO: Implement when document management is available
    };

    return profile;
  } catch (error) {
    console.error('Error in getEmployeeProfile:', error);
    return null;
  }
};

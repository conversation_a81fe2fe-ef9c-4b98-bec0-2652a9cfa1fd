import { useState, useEffect } from 'react';

/**
 * Hook to track tab visibility and detect when user returns to the tab
 * Useful for refreshing data or retrying failed operations when tab becomes visible
 */
export const useTabVisibility = () => {
  const [isVisible, setIsVisible] = useState(!document.hidden);
  const [wasHidden, setWasHidden] = useState(false);

  useEffect(() => {
    const handleVisibilityChange = () => {
      const visible = !document.hidden;
      
      if (!visible) {
        // Tab became hidden
        setWasHidden(true);
      } else if (wasHidden) {
        // Tab became visible after being hidden
        setWasHidden(false);
      }
      
      setIsVisible(visible);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [wasHidden]);

  return {
    isVisible,
    wasHidden,
    justBecameVisible: isVisible && wasHidden
  };
};

/**
 * Hook to automatically retry a function when tab becomes visible
 * Useful for retrying failed API calls or refreshing data
 */
export const useRetryOnVisible = (
  retryFn: () => void | Promise<void>,
  shouldRetry: boolean = true,
  delay: number = 1000
) => {
  const { justBecameVisible } = useTabVisibility();

  useEffect(() => {
    if (justBecameVisible && shouldRetry) {
      const timeoutId = setTimeout(() => {
        retryFn();
      }, delay);

      return () => clearTimeout(timeoutId);
    }
  }, [justBecameVisible, shouldRetry, retryFn, delay]);
};

export default useTabVisibility;

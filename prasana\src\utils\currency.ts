// Currency formatting utilities for INR

export const formatINR = (amount: number, options?: {
  showDecimals?: boolean;
  compact?: boolean;
  showSymbol?: boolean;
}): string => {
  const {
    showDecimals = true,
    compact = false,
    showSymbol = true
  } = options || {};

  if (isNaN(amount) || amount === null || amount === undefined) {
    return showSymbol ? '₹0' : '0';
  }

  // For compact format (e.g., ₹2.5L, ₹1.2Cr)
  if (compact) {
    if (amount >= 10000000) { // 1 Crore
      const crores = amount / 10000000;
      const formatted = crores.toFixed(crores >= 10 ? 0 : 1);
      return `${showSymbol ? '₹' : ''}${formatted}Cr`;
    } else if (amount >= 100000) { // 1 Lakh
      const lakhs = amount / 100000;
      const formatted = lakhs.toFixed(lakhs >= 10 ? 0 : 1);
      return `${showSymbol ? '₹' : ''}${formatted}L`;
    } else if (amount >= 1000) { // 1 Thousand
      const thousands = amount / 1000;
      const formatted = thousands.toFixed(thousands >= 10 ? 0 : 1);
      return `${showSymbol ? '₹' : ''}${formatted}K`;
    }
  }

  // Standard Indian number formatting with commas
  const formatter = new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: showDecimals ? 2 : 0,
    maximumFractionDigits: showDecimals ? 2 : 0,
  });

  let formatted = formatter.format(amount);
  
  if (!showSymbol) {
    formatted = formatted.replace('₹', '').trim();
  }

  return formatted;
};

export const formatINRCompact = (amount: number): string => {
  return formatINR(amount, { compact: true, showDecimals: false });
};

export const formatINRWithoutSymbol = (amount: number): string => {
  return formatINR(amount, { showSymbol: false });
};

export const formatINRShort = (amount: number): string => {
  return formatINR(amount, { showDecimals: false });
};

// Parse INR string back to number
export const parseINR = (inrString: string): number => {
  if (!inrString) return 0;
  
  // Remove currency symbol and commas
  const cleanString = inrString.replace(/[₹,\s]/g, '');
  
  // Handle compact formats
  if (cleanString.includes('Cr')) {
    return parseFloat(cleanString.replace('Cr', '')) * 10000000;
  } else if (cleanString.includes('L')) {
    return parseFloat(cleanString.replace('L', '')) * 100000;
  } else if (cleanString.includes('K')) {
    return parseFloat(cleanString.replace('K', '')) * 1000;
  }
  
  return parseFloat(cleanString) || 0;
};

// Format percentage with proper Indian formatting
export const formatPercentage = (value: number, decimals: number = 1): string => {
  if (isNaN(value)) return '0%';
  return `${value.toFixed(decimals)}%`;
};

// Format numbers in Indian style (lakhs, crores)
export const formatIndianNumber = (num: number): string => {
  if (num >= 10000000) {
    return `${(num / 10000000).toFixed(2)} Crore`;
  } else if (num >= 100000) {
    return `${(num / 100000).toFixed(2)} Lakh`;
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(2)} Thousand`;
  } else {
    return num.toString();
  }
};

// Salary range formatting
export const formatSalaryRange = (min: number, max: number): string => {
  return `${formatINRCompact(min)} - ${formatINRCompact(max)}`;
};

// Annual to monthly conversion
export const annualToMonthly = (annual: number): number => {
  return annual / 12;
};

// Monthly to annual conversion
export const monthlyToAnnual = (monthly: number): number => {
  return monthly * 12;
};

// Calculate percentage of amount
export const calculatePercentage = (amount: number, percentage: number): number => {
  return (amount * percentage) / 100;
};

// Calculate tax slab
export const calculateTaxSlab = (income: number, slabs: Array<{min: number, max?: number, rate: number}>): number => {
  let tax = 0;
  
  for (const slab of slabs) {
    if (income > slab.min) {
      const taxableAmount = slab.max ? Math.min(income, slab.max) - slab.min : income - slab.min;
      tax += (taxableAmount * slab.rate) / 100;
    }
  }
  
  return tax;
};

// Format employee code
export const formatEmployeeCode = (code: string): string => {
  if (!code) return 'N/A';
  return code.toUpperCase();
};

// Format department name
export const formatDepartment = (department: string): string => {
  if (!department) return 'Unassigned';
  return department.charAt(0).toUpperCase() + department.slice(1).toLowerCase();
};

// Format designation
export const formatDesignation = (designation: string): string => {
  if (!designation) return 'Employee';
  return designation.split(' ').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  ).join(' ');
};

// Format employee name
export const formatEmployeeName = (firstName: string, lastName: string): string => {
  const first = firstName?.trim() || '';
  const last = lastName?.trim() || '';
  
  if (!first && !last) return 'Unknown Employee';
  if (!last) return first;
  if (!first) return last;
  
  return `${first} ${last}`;
};

// Format date for payroll
export const formatPayrollDate = (date: string | Date): string => {
  if (!date) return 'N/A';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  return dateObj.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Format month year for periods
export const formatPeriodName = (startDate: string, endDate: string): string => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (start.getMonth() === end.getMonth() && start.getFullYear() === end.getFullYear()) {
    return start.toLocaleDateString('en-IN', {
      month: 'long',
      year: 'numeric'
    });
  }
  
  return `${start.toLocaleDateString('en-IN', { month: 'short' })} - ${end.toLocaleDateString('en-IN', { month: 'short', year: 'numeric' })}`;
};

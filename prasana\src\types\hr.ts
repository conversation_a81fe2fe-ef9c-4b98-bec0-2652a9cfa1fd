// HR-specific types for comprehensive employee management

export interface HREmployeeProfile {
  id: string;
  user_id?: string;
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string;
  personal_email?: string;
  phone?: string;
  date_of_birth?: string;
  gender?: string;
  marital_status?: string;
  nationality?: string;
  designation: string;
  department?: string;
  location?: string;
  employment_type?: string;
  joining_date?: string;
  probation_end_date?: string;
  employment_status: string;
  termination_date?: string;
  termination_reason?: string;
  
  // Contact Information
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  current_address?: string;
  permanent_address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  
  // Leave Balances
  casual_leave_balance: number;
  sick_leave_balance: number;
  annual_leave_balance: number;
  total_leave_balance: number;
  
  // Team & Management
  team_name?: string;
  manager_name?: string;
  manager_email?: string;
  
  // Performance Metrics
  active_badges_count: number;
  years_of_service: number;

  // Auth Integration
  auth_email?: string;
  auth_created_at?: string;
  profile_picture_url?: string;

  // Timestamps
  created_at: string;
  updated_at: string;
}

export interface HREmployeeBadge {
  badge_id: string;
  badge_name: string;
  badge_description?: string;
  badge_color: string;
  assigned_date: string;
  expiry_date?: string;
  days_remaining: number;
  status: 'active' | 'expired' | 'revoked';
  assigned_by_name?: string;
  notes?: string;
}

export interface HRLeaveRecord {
  leave_id: string;
  leave_type: string;
  start_date: string;
  end_date: string;
  days_count: number;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  reason?: string;
  approved_by_name?: string;
  applied_date: string;
}

export interface HREmployeeSkill {
  skill_name: string;
  proficiency_level: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert';
  years_experience?: number;
  certified: boolean;
}

export interface HREmployeeDocument {
  id: string;
  employee_id: string;
  document_name: string;
  document_type?: string;
  file_path: string;
  file_size?: number;
  mime_type?: string;
  is_confidential?: boolean;
  access_level?: string;
  uploaded_by?: string;
  uploaded_by_name?: string;
  upload_date?: string;
  expiry_date?: string;
  status?: string;
  created_at: string;
  updated_at: string;
}

export interface HRFilterOptions {
  department?: string;
  team?: string;
  employment_type?: string;
  employment_status?: string;
  location?: string;
  manager?: string;
  badge_count_min?: number;
  badge_count_max?: number;
  years_of_service_min?: number;
  years_of_service_max?: number;
  joining_date_from?: string;
  joining_date_to?: string;
}

export interface HRSearchOptions {
  query: string;
  fields: ('name' | 'email' | 'designation' | 'department' | 'employee_id')[];
}

export interface HRSortOptions {
  field: keyof HREmployeeProfile;
  direction: 'asc' | 'desc';
}

export interface HRBulkAction {
  type: 'assign_badge' | 'revoke_badge' | 'update_status' | 'export_data';
  employee_ids: string[];
  data?: any;
}

export interface HRExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  fields: (keyof HREmployeeProfile)[];
  include_badges: boolean;
  include_leave_history: boolean;
  employee_ids?: string[];
}

import React, { useState, useEffect } from 'react';
import { Key, Eye, EyeOff, CheckCircle, AlertCircle, Loader, ArrowLeft } from 'lucide-react';
import { supabase } from '../supabaseClient';
import { handleAuthCallback } from '../utils/urlHandler';

const ResetPassword: React.FC = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isValidToken, setIsValidToken] = useState(false);
  const [checkingToken, setCheckingToken] = useState(true);

  const navigateToHome = () => {
    window.location.href = '/';
  };

  // Check for valid reset token on component mount
  useEffect(() => {
    const checkResetToken = async () => {
      try {
        const authCallback = await handleAuthCallback();
        if (authCallback.isPasswordReset) {
          // Set the session with the tokens from URL
          const { error } = await supabase.auth.setSession({
            access_token: authCallback.accessToken!,
            refresh_token: authCallback.refreshToken!
          });

          if (error) {
            setError('Invalid or expired reset link. Please request a new one.');
          } else {
            setIsValidToken(true);
          }
        } else {
          setError('Invalid reset link. Please request a new password reset.');
        }
      } catch (err) {
        setError('Invalid reset link. Please request a new password reset.');
      } finally {
        setCheckingToken(false);
      }
    };

    checkResetToken();
  }, []);

  // Password validation
  const [passwordStrength, setPasswordStrength] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    number: false,
    special: false,
  });

  useEffect(() => {
    // Check password strength
    setPasswordStrength({
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[^A-Za-z0-9]/.test(password),
    });
  }, [password]);

  const isPasswordStrong = Object.values(passwordStrength).every(Boolean);
  const doPasswordsMatch = password === confirmPassword;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isPasswordStrong) {
      setError('Please ensure your password meets all requirements.');
      return;
    }
    
    if (!doPasswordsMatch) {
      setError('Passwords do not match.');
      return;
    }
    
    setLoading(true);
    setError('');

    try {
      const { error } = await supabase.auth.updateUser({
        password: password
      });

      if (error) {
        setError(error.message);
      } else {
        setSuccess(true);
        // Redirect to login after 3 seconds
        setTimeout(() => {
          navigateToHome();
        }, 3000);
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Show loading while checking token
  if (checkingToken) {
    return (
      <div className="w-full max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
        <div className="text-center">
          <Loader className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <h2 className="text-xl font-bold text-gray-900 mb-2">Verifying Reset Link</h2>
          <p className="text-gray-600">Please wait while we verify your password reset link...</p>
        </div>
      </div>
    );
  }

  // Show error if token is invalid
  if (!isValidToken) {
    return (
      <div className="w-full max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
        <div className="text-center mb-8">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <AlertCircle className="w-8 h-8 text-red-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Invalid Reset Link</h2>
          <p className="text-gray-600 mb-4">{error}</p>
        </div>

        <button
          onClick={navigateToHome}
          className="w-full py-3 px-4 flex items-center justify-center space-x-2 bg-blue-600 rounded-lg text-white font-medium hover:bg-blue-700 transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Back to Login</span>
        </button>
      </div>
    );
  }

  if (success) {
    return (
      <div className="w-full max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
        <div className="text-center mb-8">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Password Reset Successful</h2>
          <p className="text-gray-600">
            Your password has been successfully reset. You will be redirected to the login page shortly.
          </p>
        </div>

        <button
          onClick={navigateToHome}
          className="w-full py-3 px-4 flex items-center justify-center space-x-2 bg-blue-600 rounded-lg text-white font-medium hover:bg-blue-700 transition-colors"
        >
          <span>Go to Login</span>
        </button>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
      <div className="mb-8">
        <button
          onClick={navigateToHome}
          className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 mb-4 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Login</span>
        </button>
        
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Key className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Reset Password</h2>
            <p className="text-gray-600">Create a new secure password for your account.</p>
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-6 flex items-start space-x-2 p-4 bg-red-50 border border-red-200 rounded-lg">
          <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="text-sm font-medium text-red-800">Reset failed</h3>
            <p className="text-sm text-red-700 mt-1">{error}</p>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="new-password" className="block text-sm font-medium text-gray-700 mb-1">
            New Password
          </label>
          <div className="relative">
            <input
              id="new-password"
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors pr-12"
              placeholder="Enter new password"
              required
              disabled={loading}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
              aria-label={showPassword ? 'Hide password' : 'Show password'}
            >
              {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
            </button>
          </div>
          
          {/* Password strength indicators */}
          <div className="mt-3 space-y-2">
            <p className="text-sm font-medium text-gray-700">Password must contain:</p>
            <ul className="space-y-1">
              <li className={`text-xs flex items-center space-x-2 ${passwordStrength.length ? 'text-green-600' : 'text-gray-500'}`}>
                <span className={`inline-block w-2 h-2 rounded-full ${passwordStrength.length ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                <span>At least 8 characters</span>
              </li>
              <li className={`text-xs flex items-center space-x-2 ${passwordStrength.uppercase ? 'text-green-600' : 'text-gray-500'}`}>
                <span className={`inline-block w-2 h-2 rounded-full ${passwordStrength.uppercase ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                <span>At least one uppercase letter (A-Z)</span>
              </li>
              <li className={`text-xs flex items-center space-x-2 ${passwordStrength.lowercase ? 'text-green-600' : 'text-gray-500'}`}>
                <span className={`inline-block w-2 h-2 rounded-full ${passwordStrength.lowercase ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                <span>At least one lowercase letter (a-z)</span>
              </li>
              <li className={`text-xs flex items-center space-x-2 ${passwordStrength.number ? 'text-green-600' : 'text-gray-500'}`}>
                <span className={`inline-block w-2 h-2 rounded-full ${passwordStrength.number ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                <span>At least one number (0-9)</span>
              </li>
              <li className={`text-xs flex items-center space-x-2 ${passwordStrength.special ? 'text-green-600' : 'text-gray-500'}`}>
                <span className={`inline-block w-2 h-2 rounded-full ${passwordStrength.special ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                <span>At least one special character (!@#$%^&*)</span>
              </li>
            </ul>
          </div>
        </div>

        <div>
          <label htmlFor="confirm-password" className="block text-sm font-medium text-gray-700 mb-1">
            Confirm Password
          </label>
          <div className="relative">
            <input
              id="confirm-password"
              type={showConfirmPassword ? 'text' : 'password'}
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 transition-colors pr-12 ${
                confirmPassword && !doPasswordsMatch 
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                  : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
              }`}
              placeholder="Confirm new password"
              required
              disabled={loading}
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
              aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
            >
              {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
            </button>
          </div>
          {confirmPassword && !doPasswordsMatch && (
            <p className="text-xs text-red-600 mt-1">Passwords do not match</p>
          )}
        </div>

        <button
          type="submit"
          disabled={loading || !isPasswordStrong || !doPasswordsMatch}
          className={`w-full py-3 px-4 flex items-center justify-center space-x-2 rounded-lg text-white font-medium transition-colors
            ${loading || !isPasswordStrong || !doPasswordsMatch
              ? 'bg-blue-400 cursor-not-allowed' 
              : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
            }`}
        >
          {loading ? (
            <>
              <Loader className="w-5 h-5 animate-spin" />
              <span>Resetting Password...</span>
            </>
          ) : (
            <>
              <Key className="w-5 h-5" />
              <span>Reset Password</span>
            </>
          )}
        </button>
      </form>
    </div>
  );
};

export default ResetPassword;

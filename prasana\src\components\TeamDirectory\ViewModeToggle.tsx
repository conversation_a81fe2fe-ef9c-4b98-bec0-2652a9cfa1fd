import React from 'react';
import { GitBranch, Grid3X3, List, BarChart3, Users } from 'lucide-react';
import { ViewMode } from '../../types/teamDirectory';

interface ViewModeToggleProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
}

const ViewModeToggle: React.FC<ViewModeToggleProps> = ({ viewMode, onViewModeChange }) => {
  const viewModes = [
    {
      id: 'teams' as ViewMode,
      name: 'Teams',
      icon: <Users className="w-4 h-4" />,
      description: 'Organized by team structure'
    },
    {
      id: 'chart' as ViewMode,
      name: 'Org Chart',
      icon: <GitBranch className="w-4 h-4" />,
      description: 'Hierarchical organization chart'
    },
    {
      id: 'grid' as ViewMode,
      name: 'Grid',
      icon: <Grid3X3 className="w-4 h-4" />,
      description: 'Employee cards in grid layout'
    },
    {
      id: 'list' as ViewMode,
      name: 'List',
      icon: <List className="w-4 h-4" />,
      description: 'Tabular list view'
    },
    {
      id: 'analytics' as ViewMode,
      name: 'Analytics',
      icon: <BarChart3 className="w-4 h-4" />,
      description: 'Team statistics and insights'
    }
  ];

  return (
    <div className="flex items-center bg-white border border-gray-300 rounded-lg p-1">
      {viewModes.map((mode) => (
        <button
          key={mode.id}
          onClick={() => onViewModeChange(mode.id)}
          className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
            viewMode === mode.id
              ? 'bg-blue-600 text-white shadow-sm'
              : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
          }`}
          title={mode.description}
        >
          {mode.icon}
          <span className="hidden sm:inline">{mode.name}</span>
        </button>
      ))}
    </div>
  );
};

export default ViewModeToggle;

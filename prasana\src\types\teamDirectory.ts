// Team Directory specific types
import { TeamMember } from './team';

export type ViewMode = 'teams' | 'chart' | 'grid' | 'list' | 'analytics';

export type DepartmentColor = {
  NEXUS: string;
  DYNAMIX: string;
  TITAN: string;
  ATHENA: string;
};

export interface OrganizationNode {
  id: string;
  name: string;
  designation: string;
  department: string;
  avatar?: string;
  email?: string;
  phone?: string;
  level: number; // 0=CEO, 1=CRO, 2=SDM, 3=TDM/CXM, 4=Team Members
  children: OrganizationNode[];
  isExpanded?: boolean;
  parentId?: string;
  employeeData?: TeamMember;
}

export interface SearchFilters {
  searchQuery: string;
  departments: string[];
  designations: string[];
  locations: string[];
  skills: string[];
  hireDateRange: {
    start?: string;
    end?: string;
  };
  status: string[];
}

export interface TeamAnalytics {
  totalEmployees: number;
  departmentBreakdown: {
    department: string;
    count: number;
    percentage: number;
    color: string;
  }[];
  hierarchyDistribution: {
    level: string;
    count: number;
    percentage: number;
  }[];
  averageTeamSize: number;
  skillsDistribution: {
    skill: string;
    count: number;
    percentage: number;
  }[];
  growthTrends: {
    month: string;
    hires: number;
    departures: number;
    netGrowth: number;
  }[];
  locationDistribution: {
    location: string;
    count: number;
    percentage: number;
  }[];
}

export interface EmployeeProfile extends TeamMember {
  // Extended profile information
  personalInfo: {
    dateOfBirth?: string;
    gender?: string;
    maritalStatus?: string;
    nationality?: string;
    personalEmail?: string;
  };
  jobInfo: {
    employmentType?: string;
    probationEndDate?: string;
    workLocation?: string;
    salary?: {
      basic: number;
      currency: string;
    };
  };
  performance: {
    lastReviewDate?: string;
    nextReviewDate?: string;
    rating?: number;
    goals?: string[];
    achievements?: string[];
  };
  leaveInfo: {
    casualLeaveBalance: number;
    sickLeaveBalance: number;
    annualLeaveBalance: number;
    totalLeaveTaken: number;
  };
  documents: {
    id: string;
    name: string;
    type: string;
    uploadDate: string;
    url: string;
  }[];
}

export interface TeamDirectoryState {
  employees: TeamMember[];
  organizationChart: OrganizationNode[];
  filters: SearchFilters;
  viewMode: ViewMode;
  selectedEmployee: EmployeeProfile | null;
  analytics: TeamAnalytics | null;
  loading: boolean;
  error: string | null;
}

export interface QuickAction {
  id: string;
  label: string;
  icon: string;
  action: (employee: TeamMember) => void;
  color: string;
}

export const DEPARTMENT_COLORS: DepartmentColor = {
  NEXUS: '#3B82F6', // Blue
  DYNAMIX: '#10B981', // Green
  TITAN: '#F59E0B', // Orange
  ATHENA: '#8B5CF6', // Purple
};

export const HIERARCHY_LEVELS = {
  CEO: 0,
  CRO: 1,
  SDM: 2,
  MANAGEMENT: 3, // TDM/CXM
  TEAM: 4, // Team Members
} as const;

export const DESIGNATION_HIERARCHY = {
  'CEO': HIERARCHY_LEVELS.CEO,
  'Chief Revenue Officer': HIERARCHY_LEVELS.CRO,
  'Service Delivery Manager': HIERARCHY_LEVELS.SDM,
  'Technical Delivery Manager': HIERARCHY_LEVELS.MANAGEMENT,
  'Client Experience Manager': HIERARCHY_LEVELS.MANAGEMENT,
  'Associate Trainee': HIERARCHY_LEVELS.TEAM,
} as const;

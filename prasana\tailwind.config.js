/** @type {import('tailwindcss').Config} */
import colors from 'tailwindcss/colors';

export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: '#3b82f6',
        indigo: '#6366f1',
        slate: '#64748b',
        success: '#10b981',
        warning: '#f59e0b',
        danger: '#ef4444',
        gray: colors.gray,
        customGray: '#94a3b8',
      },
      animation: {
        fadeIn: 'fadeIn 0.3s ease-out',
        scaleIn: 'scaleIn 0.3s ease-out',
        slideIn: 'slideIn 0.3s ease-out',
        shimmer: 'shimmer 1.5s infinite',
        pulseGlow: 'pulse-glow 1.5s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: 0, transform: 'translateY(10px)' },
          '100%': { opacity: 1, transform: 'translateY(0)' },
        },
        scaleIn: {
          '0%': { opacity: 0, transform: 'scale(0.95)' },
          '100%': { opacity: 1, transform: 'scale(1)' },
        },
        slideIn: {
          '0%': { opacity: 0, transform: 'translateX(-10px)' },
          '100%': { opacity: 1, transform: 'translateX(0)' },
        },
        shimmer: {
          '100%': { backgroundPosition: '200px 0' },
        },
        'pulse-glow': {
          '0%, 100%': { boxShadow: '0 0 0 0 rgba(245, 158, 11, 0.7), 0 0 0 0 rgba(251, 191, 36, 0.5)' },
          '50%': { boxShadow: '0 0 0 8px rgba(245, 158, 11, 0.2), 0 0 0 16px rgba(251, 191, 36, 0.1)' },
        },
      },
    },
  },
  plugins: [],
};

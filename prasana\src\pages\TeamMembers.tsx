import React, { useState, useEffect } from 'react';
import ProfileAvatar from '../components/ProfileAvatar';
import BadgeManagement from '../components/BadgeManagement';
import BadgeImage from '../components/BadgeImage';
import { Search, Filter, Award } from 'lucide-react';
import { useUser } from '../contexts/UserContext';
import { fetchEmployeeBadges } from '../data/supabaseBadges';
import { fetchTeamMembers } from '../data/supabaseTeams';
import { TeamMember } from '../types/team';

// This function is no longer needed as we use real UUIDs from database

const TeamMembers: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTeam, setSelectedTeam] = useState<string | null>(null);
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [selectedMember, setSelectedMember] = useState<any | null>(null);
  const [showBadgeModal, setShowBadgeModal] = useState(false);
  const { isAdmin } = useUser();
  const [memberBadges, setMemberBadges] = useState<Record<string, any[]>>({});
  
  useEffect(() => {
    loadAllMemberBadges();
  }, []);

  // State for team members from database
  const [allMembers, setAllMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);

  // Load team members from database
  useEffect(() => {
    const loadTeamMembers = async () => {
      try {
        setLoading(true);
        const members = await fetchTeamMembers();
        setAllMembers(members);

        // Load badges for all members after getting the real member data
        await loadAllMemberBadges(members);
      } catch (error) {
        console.error('Error loading team members:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTeamMembers();
  }, []);

  // Load badges for all members
  const loadAllMemberBadges = async (members: TeamMember[] = allMembers) => {
    try {
      const badgePromises = members.map(async (member) => {
        if (!member.id) {
          return { memberId: '', badges: [] };
        }

        try {
          const badges = await fetchEmployeeBadges(member.id);
          return { memberId: member.id, badges };
        } catch (error) {
          return { memberId: member.id, badges: [] };
        }
      });

      const results = await Promise.all(badgePromises);
      const badgeMap: Record<string, any[]> = {};
      results.forEach(({ memberId, badges }) => {
        if (memberId && badges && Array.isArray(badges)) {
          badgeMap[memberId] = badges;
        } else {
          badgeMap[memberId] = [];
        }
      });
      setMemberBadges(badgeMap);
    } catch (error) {
      console.error('Error loading member badges:', error);
    }
  };

  // Calculate days remaining for a badge
  const calculateDaysRemaining = (expiryDate: string): number => {
    if (!expiryDate) return -1; // No expiry
    const expiry = new Date(expiryDate);
    const now = new Date();
    const diffTime = expiry.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // Filter members based on search and filters
  const filteredMembers = allMembers.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         member.role.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (member.employeeCode && member.employeeCode.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesTeam = !selectedTeam || member.team === selectedTeam;
    const matchesRole = !selectedRole || member.role === selectedRole;
    const matchesStatus = !selectedStatus || member.status === selectedStatus;
    return matchesSearch && matchesTeam && matchesRole && matchesStatus;
  });

  // Get unique roles and statuses for filters
  const uniqueRoles = Array.from(new Set(allMembers.map(m => m.role)));
  const uniqueStatuses = Array.from(new Set(allMembers.map(m => m.status)));

  // Show loading state
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Team Members</h1>
          <p className="text-gray-600">Loading team members...</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[1, 2, 3, 4, 5, 6, 7, 8].map(i => (
            <div key={i} className="bg-white rounded-lg shadow-sm border p-4 animate-pulse">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-32 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Team Members</h1>
        <p className="text-gray-600">View all employees and their achievements across different teams and departments</p>
        <div className="mt-3 flex items-center gap-4 text-sm">
          <span className="flex items-center gap-1 text-green-600 bg-green-50 px-3 py-1 rounded-full">
            <Award size={14} />
            All users can view member badges and achievements
          </span>
          {isAdmin && (
            <span className="flex items-center gap-1 text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
              <Award size={14} />
              Admin: Can manage badges
            </span>
          )}
        </div>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <input
              type="text"
              placeholder="Search by name, role, or PS Number..."
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <div className="flex gap-4">
          <select
            className="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={selectedTeam || ''}
            onChange={(e) => setSelectedTeam(e.target.value || null)}
          >
            <option value="">All Teams</option>
            <option value="TITAN">TITAN</option>
            <option value="NEXUS">NEXUS</option>
            <option value="ATHENA">ATHENA</option>
            <option value="DYNAMIX">DYNAMIX</option>
          </select>
          <select
            className="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={selectedRole || ''}
            onChange={(e) => setSelectedRole(e.target.value || null)}
          >
            <option value="">All Roles</option>
            {uniqueRoles.map(role => (
              <option key={role} value={role}>{role}</option>
            ))}
          </select>
          <select
            className="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={selectedStatus || ''}
            onChange={(e) => setSelectedStatus(e.target.value || null)}
          >
            <option value="">All Status</option>
            {uniqueStatuses.map(status => (
              <option key={status} value={status}>
                {status === 'active' ? 'Active' : status === 'terminated' ? 'Terminated' : status}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Team Members Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredMembers.map((member, index) => (
          <div
            key={index}
            className={`bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow h-full flex flex-col ${
              member.isLeadership ? 'border-blue-200' : 'border-gray-200'
            }`}
          >
            {/* Header Section with Avatar and Basic Info */}
            <div className="flex items-start space-x-4 mb-4">
              <ProfileAvatar
                name={member.name}
                avatar={member.avatar}
                size="lg"
                team={member.team as any}
              />
              <div className="flex-1 min-w-0">
                <div className="mb-2">
                  <h3 className="font-semibold text-gray-900 truncate text-base">{member.name}</h3>
                  <p className="text-sm text-gray-600 mt-1">{member.role}</p>
                </div>
                {member.employeeCode && (
                  <p className="text-xs text-blue-600 font-medium bg-blue-50 px-2 py-1 rounded inline-block">
                    PS Number: {member.employeeCode}
                  </p>
                )}
                {member.designation && (
                  <p className="text-xs text-gray-500 mt-2">{member.designation}</p>
                )}
              </div>
              {/* Badge Count Indicator - Positioned better */}
              {memberBadges[member.id]?.length > 0 && (
                <div className="flex items-center gap-1 bg-blue-100 text-blue-800 px-2 py-1 rounded-full flex-shrink-0">
                  <Award size={12} />
                  <span className="text-xs font-medium">{memberBadges[member.id].length}</span>
                </div>
              )}
            </div>

            {/* Tags Section */}
            <div className="flex flex-wrap gap-2 mb-4">
              <span className={`inline-block px-3 py-1 text-xs font-medium rounded-full ${
                member.team === 'TITAN' ? 'bg-blue-100 text-blue-800' :
                member.team === 'NEXUS' ? 'bg-purple-100 text-purple-800' :
                member.team === 'ATHENA' ? 'bg-green-100 text-green-800' :
                'bg-amber-100 text-amber-800'
              }`}>
                {member.team}
              </span>
              {member.isLeadership && (
                <span className="inline-block px-3 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                  Leadership
                </span>
              )}
              <span className={`inline-block px-3 py-1 text-xs font-medium rounded-full ${
                member.status === 'active' ? 'bg-green-100 text-green-800' :
                member.status === 'terminated' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {member.status === 'active' ? 'Active' :
                 member.status === 'terminated' ? 'Terminated' :
                 member.status}
              </span>
            </div>
            {/* Badges Section - Only show if user has badges */}
            <div className="flex-1">
              {memberBadges[member.id]?.length > 0 && (
                <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <Award size={16} className="text-blue-600" />
                      <span className="text-sm font-medium text-gray-700">Achievements</span>
                    </div>
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium">
                      {memberBadges[member.id]?.length || 0} Badge{(memberBadges[member.id]?.length || 0) !== 1 ? 's' : ''}
                    </span>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    {memberBadges[member.id].map((badge: any) => {
                      const daysRemaining = badge.expiry_date ? calculateDaysRemaining(badge.expiry_date) : -1;
                      return (
                        <div key={badge.id} className="relative group">
                          <BadgeImage
                            badge={badge}
                            size="sm"
                            daysRemaining={daysRemaining}
                            awardedDate={badge.assigned_date}
                          />
                          {/* Enhanced tooltip for all users */}
                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                            <div className="font-medium">{badge.badge_type?.name || badge.name}</div>
                            <div className="text-gray-300">Awarded: {new Date(badge.assigned_date).toLocaleDateString()}</div>
                            {badge.expiry_date && (
                              <div className="text-gray-300">
                                Expires: {new Date(badge.expiry_date).toLocaleDateString()}
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>

            {/* Admin Actions - Always at bottom */}
            {isAdmin && (
              <div className="mt-4 pt-4 border-t border-gray-100">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedMember(member);
                    setShowBadgeModal(true);
                  }}
                  className="w-full text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 flex items-center justify-center gap-2 py-2 px-3 rounded-lg transition-colors"
                >
                  <Award size={16} />
                  Manage Badges
                </button>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredMembers.length === 0 && (
        <div className="text-center py-12">
          <Filter className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No members found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search or filter criteria
          </p>
        </div>
      )}



      {/* Badge Management Modal (Admin Only) */}
      {isAdmin && showBadgeModal && selectedMember && (
        <BadgeManagement
          userId={selectedMember.id}
          userName={selectedMember.name}
          onClose={() => {
            setShowBadgeModal(false);
            loadAllMemberBadges(allMembers); // Refresh badges after modal closes
          }}
        />
      )}
    </div>
  );
};

export default TeamMembers; 
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '../supabaseClient';
import { UserRole } from '../types';

interface User {
  id: string;
  name: string;
  email: string;
  role?: UserRole;
  isAdmin: boolean;
  isSuperAdmin: boolean;
}

interface UserContextType {
  currentUser: User | null;
  user: User | null; // Alias for currentUser for compatibility
  isAdmin: boolean;
  isSuperAdmin: boolean;
  login: (email: string, password: string) => Promise<{ error: string | null }>;
  signup: (email: string, password: string, fullName?: string) => Promise<{ error: string | null }>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: string | null }>;
  updatePassword: (password: string) => Promise<{ error: string | null }>;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

// List of admin emails (add your admin emails here)
const ADMIN_EMAILS = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];

// List of super admin emails (highest level access)
const SUPER_ADMIN_EMAILS = ['<EMAIL>', '<EMAIL>'];

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);

  useEffect(() => {
    // Check for active session on mount
    const session = supabase.auth.getSession().then(({ data: { session } }) => {
      if (session?.user) {
        const email = session.user.email || '';
        const isUserAdmin = ADMIN_EMAILS.includes(email);
        const isUserSuperAdmin = SUPER_ADMIN_EMAILS.includes(email);
        setCurrentUser({
          id: session.user.id,
          name: session.user.user_metadata?.name || email,
          email,
          isAdmin: isUserAdmin,
          isSuperAdmin: isUserSuperAdmin
        });
        setIsAdmin(isUserAdmin);
        setIsSuperAdmin(isUserSuperAdmin);
      }
    });
    // Listen for auth state changes
    const { data: listener } = supabase.auth.onAuthStateChange((_event, session) => {
      if (session?.user) {
        const email = session.user.email || '';
        const isUserAdmin = ADMIN_EMAILS.includes(email);
        const isUserSuperAdmin = SUPER_ADMIN_EMAILS.includes(email);
        setCurrentUser({
          id: session.user.id,
          name: session.user.user_metadata?.name || email,
          email,
          isAdmin: isUserAdmin,
          isSuperAdmin: isUserSuperAdmin
        });
        setIsAdmin(isUserAdmin);
        setIsSuperAdmin(isUserSuperAdmin);
      } else {
        setCurrentUser(null);
        setIsAdmin(false);
        setIsSuperAdmin(false);
      }
    });
    return () => {
      listener?.subscription.unsubscribe();
    };
  }, []);

  const login = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({ email, password });
    if (error) {
      return { error: error.message };
    }
    // User will be set by the auth state change listener
    return { error: null };
  };

  const signup = async (email: string, password: string, fullName?: string) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName || email.split('@')[0],
        }
      }
    });

    if (error) {
      return { error: error.message };
    }

    // Check if email confirmation is required
    if (data.user && !data.session) {
      return { error: 'Please check your email for a confirmation link.' };
    }

    return { error: null };
  };

  const logout = async () => {
    await supabase.auth.signOut();
    setCurrentUser(null);
    setIsAdmin(false);
    setIsSuperAdmin(false);
  };

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });

    if (error) {
      return { error: error.message };
    }

    return { error: null };
  };

  const updatePassword = async (newPassword: string) => {
    const { error } = await supabase.auth.updateUser({
      password: newPassword
    });

    if (error) {
      return { error: error.message };
    }

    return { error: null };
  };

  return (
    <UserContext.Provider value={{
      currentUser,
      user: currentUser, // Alias for compatibility
      isAdmin,
      isSuperAdmin,
      login,
      signup,
      logout,
      resetPassword,
      updatePassword
    }}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}; 
import { ProfileUpdateData } from '../services/profileService';

export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate phone number format (basic validation)
 */
export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
};

/**
 * Validate date format and ensure it's not in the future for birth dates
 */
export const isValidBirthDate = (dateString: string): boolean => {
  const date = new Date(dateString);
  const today = new Date();
  
  // Check if date is valid
  if (isNaN(date.getTime())) {
    return false;
  }
  
  // Check if date is not in the future
  if (date > today) {
    return false;
  }
  
  // Check if date is reasonable (not too old)
  const minDate = new Date();
  minDate.setFullYear(today.getFullYear() - 120);
  if (date < minDate) {
    return false;
  }
  
  return true;
};

/**
 * Validate postal code format (basic validation)
 */
export const isValidPostalCode = (postalCode: string): boolean => {
  // Basic validation - alphanumeric, spaces, hyphens allowed
  const postalRegex = /^[A-Za-z0-9\s\-]{3,10}$/;
  return postalRegex.test(postalCode);
};

/**
 * Sanitize text input to prevent XSS
 */
export const sanitizeText = (text: string): string => {
  return text
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
};

/**
 * Validate profile update data
 */
export const validateProfileUpdate = (data: ProfileUpdateData): ValidationResult => {
  const errors: ValidationError[] = [];

  // Validate full name
  if (data.full_name !== undefined) {
    if (data.full_name.length < 2) {
      errors.push({ field: 'full_name', message: 'Full name must be at least 2 characters long' });
    }
    if (data.full_name.length > 100) {
      errors.push({ field: 'full_name', message: 'Full name must be less than 100 characters' });
    }
  }

  // Validate phone
  if (data.phone !== undefined && data.phone.length > 0) {
    if (!isValidPhone(data.phone)) {
      errors.push({ field: 'phone', message: 'Please enter a valid phone number' });
    }
  }

  // Validate personal email
  if (data.personal_email !== undefined && data.personal_email.length > 0) {
    if (!isValidEmail(data.personal_email)) {
      errors.push({ field: 'personal_email', message: 'Please enter a valid email address' });
    }
  }

  // Validate first name
  if (data.first_name !== undefined) {
    if (data.first_name.length < 1) {
      errors.push({ field: 'first_name', message: 'First name is required' });
    }
    if (data.first_name.length > 50) {
      errors.push({ field: 'first_name', message: 'First name must be less than 50 characters' });
    }
  }

  // Validate last name
  if (data.last_name !== undefined) {
    if (data.last_name.length < 1) {
      errors.push({ field: 'last_name', message: 'Last name is required' });
    }
    if (data.last_name.length > 50) {
      errors.push({ field: 'last_name', message: 'Last name must be less than 50 characters' });
    }
  }

  // Validate date of birth
  if (data.date_of_birth !== undefined && data.date_of_birth.length > 0) {
    if (!isValidBirthDate(data.date_of_birth)) {
      errors.push({ field: 'date_of_birth', message: 'Please enter a valid birth date' });
    }
  }

  // Validate gender
  if (data.gender !== undefined && data.gender.length > 0) {
    const validGenders = ['Male', 'Female', 'Other', 'Prefer not to say'];
    if (!validGenders.includes(data.gender)) {
      errors.push({ field: 'gender', message: 'Please select a valid gender option' });
    }
  }

  // Validate marital status
  if (data.marital_status !== undefined && data.marital_status.length > 0) {
    const validStatuses = ['Single', 'Married', 'Divorced', 'Widowed'];
    if (!validStatuses.includes(data.marital_status)) {
      errors.push({ field: 'marital_status', message: 'Please select a valid marital status' });
    }
  }

  // Validate nationality
  if (data.nationality !== undefined && data.nationality.length > 0) {
    if (data.nationality.length > 50) {
      errors.push({ field: 'nationality', message: 'Nationality must be less than 50 characters' });
    }
  }

  // Validate addresses
  if (data.current_address !== undefined && data.current_address.length > 500) {
    errors.push({ field: 'current_address', message: 'Current address must be less than 500 characters' });
  }

  if (data.permanent_address !== undefined && data.permanent_address.length > 500) {
    errors.push({ field: 'permanent_address', message: 'Permanent address must be less than 500 characters' });
  }

  // Validate city, state, country
  if (data.city !== undefined && data.city.length > 100) {
    errors.push({ field: 'city', message: 'City must be less than 100 characters' });
  }

  if (data.state !== undefined && data.state.length > 100) {
    errors.push({ field: 'state', message: 'State must be less than 100 characters' });
  }

  if (data.country !== undefined && data.country.length > 100) {
    errors.push({ field: 'country', message: 'Country must be less than 100 characters' });
  }

  // Validate postal code
  if (data.postal_code !== undefined && data.postal_code.length > 0) {
    if (!isValidPostalCode(data.postal_code)) {
      errors.push({ field: 'postal_code', message: 'Please enter a valid postal code' });
    }
  }

  // Validate emergency contact phone
  if (data.emergency_contact_phone !== undefined && data.emergency_contact_phone.length > 0) {
    if (!isValidPhone(data.emergency_contact_phone)) {
      errors.push({ field: 'emergency_contact_phone', message: 'Please enter a valid emergency contact phone number' });
    }
  }

  // Validate emergency contact name
  if (data.emergency_contact_name !== undefined && data.emergency_contact_name.length > 100) {
    errors.push({ field: 'emergency_contact_name', message: 'Emergency contact name must be less than 100 characters' });
  }

  // Validate emergency contact relationship
  if (data.emergency_contact_relationship !== undefined && data.emergency_contact_relationship.length > 50) {
    errors.push({ field: 'emergency_contact_relationship', message: 'Emergency contact relationship must be less than 50 characters' });
  }

  // Validate bio
  if (data.bio !== undefined && data.bio.length > 1000) {
    errors.push({ field: 'bio', message: 'Bio must be less than 1000 characters' });
  }

  // Validate skills array
  if (data.skills !== undefined) {
    if (data.skills.length > 20) {
      errors.push({ field: 'skills', message: 'Maximum 20 skills allowed' });
    }
    
    for (const skill of data.skills) {
      if (skill.length > 50) {
        errors.push({ field: 'skills', message: 'Each skill must be less than 50 characters' });
        break;
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Sanitize profile update data
 */
export const sanitizeProfileUpdate = (data: ProfileUpdateData): ProfileUpdateData => {
  const sanitized: ProfileUpdateData = {};

  // Sanitize text fields
  if (data.full_name !== undefined) sanitized.full_name = sanitizeText(data.full_name);
  if (data.first_name !== undefined) sanitized.first_name = sanitizeText(data.first_name);
  if (data.last_name !== undefined) sanitized.last_name = sanitizeText(data.last_name);
  if (data.nationality !== undefined) sanitized.nationality = sanitizeText(data.nationality);
  if (data.personal_email !== undefined) sanitized.personal_email = sanitizeText(data.personal_email);
  if (data.current_address !== undefined) sanitized.current_address = sanitizeText(data.current_address);
  if (data.permanent_address !== undefined) sanitized.permanent_address = sanitizeText(data.permanent_address);
  if (data.city !== undefined) sanitized.city = sanitizeText(data.city);
  if (data.state !== undefined) sanitized.state = sanitizeText(data.state);
  if (data.country !== undefined) sanitized.country = sanitizeText(data.country);
  if (data.postal_code !== undefined) sanitized.postal_code = sanitizeText(data.postal_code);
  if (data.emergency_contact_name !== undefined) sanitized.emergency_contact_name = sanitizeText(data.emergency_contact_name);
  if (data.emergency_contact_relationship !== undefined) sanitized.emergency_contact_relationship = sanitizeText(data.emergency_contact_relationship);
  if (data.bio !== undefined) sanitized.bio = sanitizeText(data.bio);

  // Sanitize phone numbers (remove non-numeric characters except +, -, (, ), space)
  if (data.phone !== undefined) {
    sanitized.phone = data.phone.replace(/[^\d\+\-\(\)\s]/g, '');
  }
  if (data.emergency_contact_phone !== undefined) {
    sanitized.emergency_contact_phone = data.emergency_contact_phone.replace(/[^\d\+\-\(\)\s]/g, '');
  }

  // Copy other fields as-is (they don't need sanitization)
  if (data.avatar_url !== undefined) sanitized.avatar_url = data.avatar_url;
  if (data.date_of_birth !== undefined) sanitized.date_of_birth = data.date_of_birth;
  if (data.gender !== undefined) sanitized.gender = data.gender;
  if (data.marital_status !== undefined) sanitized.marital_status = data.marital_status;

  // Sanitize skills array
  if (data.skills !== undefined) {
    sanitized.skills = data.skills.map(skill => sanitizeText(skill));
  }

  return sanitized;
};

/**
 * Validate file upload for profile pictures
 */
export const validateProfilePicture = (file: File): ValidationResult => {
  const errors: ValidationError[] = [];

  // Check file size (5MB limit)
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    errors.push({ field: 'file', message: 'File size must be less than 5MB' });
  }

  // Check file type
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
  if (!allowedTypes.includes(file.type)) {
    errors.push({ field: 'file', message: 'File must be a JPEG, PNG, WebP, or GIF image' });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/* Enhanced Analytics Dashboard Styles */

/* Professional animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Dashboard components */
.analytics-dashboard {
  animation: fadeInUp 0.6s ease-out;
}

.metric-card {
  animation: scaleIn 0.4s ease-out;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.chart-container {
  animation: slideInLeft 0.5s ease-out;
  transition: all 0.3s ease;
}

.chart-container:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* Loading states */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Professional chart enhancements */
.recharts-wrapper {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 14px;
}

.recharts-tooltip-wrapper {
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.15));
}

.recharts-tooltip-content {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.recharts-legend-wrapper {
  padding-top: 20px !important;
}

.recharts-cartesian-axis-tick-value {
  fill: #6b7280 !important;
  font-size: 12px !important;
}

.recharts-cartesian-grid-horizontal line,
.recharts-cartesian-grid-vertical line {
  stroke: #f3f4f6 !important;
}

/* Professional chart colors */
.recharts-bar {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.recharts-pie-sector {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.recharts-area {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* Performance indicators */
.performance-indicator {
  position: relative;
  overflow: hidden;
}

.performance-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.performance-indicator:hover::before {
  left: 100%;
}

/* Professional metric cards with better contrast */
.metric-card {
  border-left: 4px solid #e5e7eb;
  transition: all 0.3s ease;
  background: #ffffff;
}

.metric-card:hover {
  border-left-color: #3B82F6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

/* Enhanced color variants for accessibility */
.metric-card-blue {
  border-left-color: #3B82F6;
}

.metric-card-green {
  border-left-color: #10B981;
}

.metric-card-purple {
  border-left-color: #8B5CF6;
}

.metric-card-orange {
  border-left-color: #F59E0B;
}

.metric-card-red {
  border-left-color: #EF4444;
}

/* Interactive elements */
.interactive-button {
  transition: all 0.2s ease;
}

.interactive-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.interactive-button:active {
  transform: translateY(0);
}

/* Section toggles */
.section-toggle {
  transition: all 0.2s ease;
}

.section-toggle:hover {
  background-color: #f3f4f6;
  border-radius: 6px;
}

/* Filter controls */
.filter-control {
  transition: all 0.2s ease;
}

.filter-control:focus {
  ring: 2px;
  ring-color: #3B82F6;
  ring-opacity: 0.5;
  border-color: #3B82F6;
}

/* Top performers list */
.performer-item {
  transition: all 0.2s ease;
}

.performer-item:hover {
  background-color: #f8fafc;
  transform: translateX(4px);
}

/* Team stats */
.team-stat-item {
  transition: all 0.2s ease;
  border-radius: 8px;
}

.team-stat-item:hover {
  background-color: #f1f5f9;
}

/* Insights cards */
.insight-card {
  transition: all 0.3s ease;
}

.insight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .analytics-dashboard {
    padding: 1rem;
  }
  
  .metric-card {
    margin-bottom: 1rem;
  }
  
  .chart-container {
    margin-bottom: 1.5rem;
  }
  
  .recharts-wrapper {
    font-size: 12px;
  }
}

@media (max-width: 640px) {
  .metric-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-grid {
    grid-template-columns: 1fr;
  }
  
  .performance-grid {
    grid-template-columns: 1fr;
  }
}

/* Print styles */
@media print {
  .analytics-dashboard {
    background: white !important;
  }
  
  .interactive-button,
  .section-toggle,
  .filter-control {
    display: none !important;
  }
  
  .chart-container {
    break-inside: avoid;
    margin-bottom: 2rem;
  }
  
  .metric-card {
    break-inside: avoid;
    margin-bottom: 1rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .analytics-dashboard {
    background-color: #1f2937;
    color: #f9fafb;
  }
  
  .metric-card,
  .chart-container {
    background-color: #374151;
    border-color: #4b5563;
  }
  
  .loading-skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus indicators */
.focus-visible {
  outline: 2px solid #3B82F6;
  outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .metric-card,
  .chart-container {
    border: 2px solid;
  }
  
  .interactive-button {
    border: 2px solid;
  }
}

import React, { useState, useEffect } from 'react';
import { UserPlus, Users, Settings, Shield, Mail, Key, Users2, Save, AlertCircle, CheckCircle, Edit, X, RotateCcw } from 'lucide-react';
import { supabase } from '../supabaseClient';
import AuthSyncManager from './AuthSyncManager';

interface Team {
  id: string;
  name: string;
  description: string;
}

interface Employee {
  id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  designation: string;
  department: string;
  team_id: string;
  status: string;
}

const UserManagementPanel: React.FC = () => {
  const [teams, setTeams] = useState<Team[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'create' | 'manage' | 'sync'>('create');
  const [editingEmployee, setEditingEmployee] = useState<string | null>(null);
  const [editForm, setEditForm] = useState({
    email: '',
    newPassword: '',
    firstName: '',
    lastName: '',
    designation: '',
    teamId: ''
  });
  
  // Create user form state
  const [newUser, setNewUser] = useState({
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    designation: '',
    teamId: '',
    department: ''
  });

  // Result state
  const [result, setResult] = useState<{
    type: 'success' | 'error' | 'info';
    message: string;
  } | null>(null);

  useEffect(() => {
    loadTeams();
    loadEmployees();
  }, []);

  const loadTeams = async () => {
    try {
      const { data, error } = await supabase
        .from('teams')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      setTeams(data || []);
    } catch (error) {
      console.error('Error loading teams:', error);
    }
  };

  const loadEmployees = async () => {
    try {
      const { data, error } = await supabase
        .from('employees')
        .select(`
          *,
          teams!team_id(name)
        `)
        .eq('status', 'active')
        .neq('designation', 'System Administrator')
        .order('first_name');

      if (error) throw error;
      setEmployees(data || []);
    } catch (error) {
      console.error('Error loading employees:', error);
    }
  };

  const createUser = async () => {
    if (!newUser.email || !newUser.password || !newUser.firstName) {
      setResult({
        type: 'error',
        message: 'Please fill in all required fields (email, password, first name)'
      });
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      // Step 1: Create auth user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: newUser.email,
        password: newUser.password,
        options: {
          data: {
            full_name: `${newUser.firstName} ${newUser.lastName}`.trim()
          }
        }
      });

      if (authError) throw authError;

      // Step 2: Create employee record
      const { error: employeeError } = await supabase
        .from('employees')
        .insert({
          user_id: authData.user?.id,
          employee_id: `EMP_${Date.now()}`,
          first_name: newUser.firstName,
          last_name: newUser.lastName,
          email: newUser.email,
          designation: newUser.designation || 'Associate Trainee',
          department: newUser.department || teams.find(t => t.id === newUser.teamId)?.name || 'General',
          team_id: newUser.teamId || null,
          status: 'active',
          joining_date: new Date().toISOString().split('T')[0]
        });

      if (employeeError) throw employeeError;

      setResult({
        type: 'success',
        message: `User ${newUser.firstName} created successfully and assigned to team!`
      });

      // Reset form
      setNewUser({
        email: '',
        password: '',
        firstName: '',
        lastName: '',
        designation: '',
        teamId: '',
        department: ''
      });

      // Reload employees
      loadEmployees();

    } catch (error: any) {
      setResult({
        type: 'error',
        message: `Error creating user: ${error.message}`
      });
    } finally {
      setLoading(false);
    }
  };

  const updateEmployeeTeam = async (employeeId: string, teamId: string) => {
    try {
      const { error } = await supabase
        .from('employees')
        .update({
          team_id: teamId,
          department: teams.find(t => t.id === teamId)?.name || 'General'
        })
        .eq('id', employeeId);

      if (error) throw error;

      setResult({
        type: 'success',
        message: 'Employee team assignment updated successfully!'
      });

      loadEmployees();
    } catch (error: any) {
      setResult({
        type: 'error',
        message: `Error updating team assignment: ${error.message}`
      });
    }
  };

  const startEditEmployee = (employee: Employee) => {
    setEditingEmployee(employee.id);
    setEditForm({
      email: employee.email,
      newPassword: '',
      firstName: employee.first_name,
      lastName: employee.last_name,
      designation: employee.designation,
      teamId: employee.team_id || ''
    });
  };

  const cancelEdit = () => {
    setEditingEmployee(null);
    setEditForm({
      email: '',
      newPassword: '',
      firstName: '',
      lastName: '',
      designation: '',
      teamId: ''
    });
  };

  const sendPasswordReset = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      });

      if (error) throw error;

      setResult({
        type: 'success',
        message: `Password reset email sent to ${email}. User can check their email to reset password.`
      });
    } catch (error: any) {
      setResult({
        type: 'error',
        message: `Error sending password reset: ${error.message}`
      });
    }
  };

  const setTempPassword = async (email: string, tempPassword: string = 'TempPassword123!') => {
    try {
      // Find the user by email
      const { data: users, error: fetchError } = await supabase
        .from('auth.users')
        .select('id')
        .eq('email', email);

      if (fetchError) throw fetchError;
      if (!users || users.length === 0) throw new Error('User not found');

      const userId = users[0].id;

      // Set temporary password using SQL
      const { error } = await supabase.rpc('set_temp_password', {
        user_id: userId,
        temp_password: tempPassword
      });

      if (error) {
        // Fallback: try direct SQL update
        const { error: sqlError } = await supabase
          .from('auth.users')
          .update({
            encrypted_password: `crypt('${tempPassword}', gen_salt('bf'))`,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        if (sqlError) throw sqlError;
      }

      setResult({
        type: 'success',
        message: `Temporary password set for ${email}. Password: ${tempPassword}`
      });
    } catch (error: any) {
      setResult({
        type: 'error',
        message: `Error setting password: ${error.message}`
      });
    }
  };

  const saveEmployeeChanges = async (employee: Employee) => {
    setLoading(true);
    try {
      // Update employee record
      const { error: employeeError } = await supabase
        .from('employees')
        .update({
          first_name: editForm.firstName,
          last_name: editForm.lastName,
          email: editForm.email,
          designation: editForm.designation,
          team_id: editForm.teamId || null,
          department: teams.find(t => t.id === editForm.teamId)?.name || 'General'
        })
        .eq('id', employee.id);

      if (employeeError) throw employeeError;

      // Note: Auth system updates require server-side admin functions
      // For now, we only update the employee record
      let authUpdateMessage = '';

      if (editForm.email !== employee.email || editForm.newPassword) {
        authUpdateMessage = ' Note: For login credential changes, the user may need to use password reset or contact admin.';
      }

      setResult({
        type: 'success',
        message: `Employee information updated successfully!${authUpdateMessage}`
      });

      setEditingEmployee(null);
      loadEmployees();

    } catch (error: any) {
      setResult({
        type: 'error',
        message: `Error updating employee: ${error.message}`
      });
    } finally {
      setLoading(false);
    }
  };

  const designations = [
    'Associate Trainee',
    'Developer',
    'Senior Developer',
    'Technical Delivery Manager',
    'Service Delivery Manager',
    'Client Experience Manager',
    'System Administrator'
  ];

  return (
    <div className="max-w-6xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-blue-100 rounded-lg">
          <Shield className="w-6 h-6 text-blue-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900">User Management Panel</h2>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('create')}
          className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2 rounded-md transition-colors ${
            activeTab === 'create'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <UserPlus className="w-4 h-4" />
          <span>Create User</span>
        </button>
        <button
          onClick={() => setActiveTab('manage')}
          className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2 rounded-md transition-colors ${
            activeTab === 'manage'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Users className="w-4 h-4" />
          <span>Manage Users</span>
        </button>
        <button
          onClick={() => setActiveTab('sync')}
          className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2 rounded-md transition-colors ${
            activeTab === 'sync'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <RotateCcw className="w-4 h-4" />
          <span>Sync System</span>
        </button>
      </div>

      {/* Result Message */}
      {result && (
        <div className={`mb-6 p-4 rounded-lg border ${
          result.type === 'success' 
            ? 'bg-green-50 border-green-200 text-green-800'
            : result.type === 'error'
            ? 'bg-red-50 border-red-200 text-red-800'
            : 'bg-blue-50 border-blue-200 text-blue-800'
        }`}>
          <div className="flex items-center space-x-2">
            {result.type === 'success' ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <AlertCircle className="w-5 h-5" />
            )}
            <span>{result.message}</span>
          </div>
        </div>
      )}

      {activeTab === 'create' && (
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-gray-900">Create New User</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email Address *
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="email"
                  value={newUser.email}
                  onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                  disabled={loading}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Password *
              </label>
              <div className="relative">
                <Key className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="password"
                  value={newUser.password}
                  onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Password123!"
                  disabled={loading}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                First Name *
              </label>
              <input
                type="text"
                value={newUser.firstName}
                onChange={(e) => setNewUser({ ...newUser, firstName: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="John"
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Last Name
              </label>
              <input
                type="text"
                value={newUser.lastName}
                onChange={(e) => setNewUser({ ...newUser, lastName: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Doe"
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Designation
              </label>
              <select
                value={newUser.designation}
                onChange={(e) => setNewUser({ ...newUser, designation: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={loading}
              >
                <option value="">Select Designation</option>
                {designations.map(designation => (
                  <option key={designation} value={designation}>{designation}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Team Assignment
              </label>
              <div className="relative">
                <Users2 className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <select
                  value={newUser.teamId}
                  onChange={(e) => setNewUser({ ...newUser, teamId: e.target.value })}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  disabled={loading}
                >
                  <option value="">Select Team</option>
                  {teams.map(team => (
                    <option key={team.id} value={team.id}>{team.name}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          <button
            onClick={createUser}
            disabled={loading}
            className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-blue-400 transition-colors"
          >
            {loading ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <UserPlus className="w-5 h-5" />
            )}
            <span>{loading ? 'Creating User...' : 'Create User & Assign Team'}</span>
          </button>
        </div>
      )}

      {activeTab === 'manage' && (
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Manage Existing Users</h3>
            <p className="text-sm text-gray-600 mt-1">
              Click "Edit" to modify user credentials, personal information, and team assignments.
              You can change email addresses, reset passwords, and update designations.
            </p>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border border-gray-300 px-4 py-2 text-left">Name</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Email</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Designation</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Team</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {employees.map(employee => (
                  <tr key={employee.id} className="hover:bg-gray-50">
                    {editingEmployee === employee.id ? (
                      // Edit Mode
                      <>
                        <td className="border border-gray-300 px-2 py-2">
                          <div className="space-y-1">
                            <input
                              type="text"
                              value={editForm.firstName}
                              onChange={(e) => setEditForm({...editForm, firstName: e.target.value})}
                              className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                              placeholder="First Name"
                            />
                            <input
                              type="text"
                              value={editForm.lastName}
                              onChange={(e) => setEditForm({...editForm, lastName: e.target.value})}
                              className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                              placeholder="Last Name"
                            />
                          </div>
                        </td>
                        <td className="border border-gray-300 px-2 py-2">
                          <input
                            type="email"
                            value={editForm.email}
                            onChange={(e) => setEditForm({...editForm, email: e.target.value})}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                            placeholder="Email"
                          />
                          <input
                            type="password"
                            value={editForm.newPassword}
                            onChange={(e) => setEditForm({...editForm, newPassword: e.target.value})}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm mt-1"
                            placeholder="New Password (optional)"
                          />
                        </td>
                        <td className="border border-gray-300 px-2 py-2">
                          <select
                            value={editForm.designation}
                            onChange={(e) => setEditForm({...editForm, designation: e.target.value})}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          >
                            {designations.map(designation => (
                              <option key={designation} value={designation}>{designation}</option>
                            ))}
                          </select>
                        </td>
                        <td className="border border-gray-300 px-2 py-2">
                          <select
                            value={editForm.teamId}
                            onChange={(e) => setEditForm({...editForm, teamId: e.target.value})}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          >
                            <option value="">No Team</option>
                            {teams.map(team => (
                              <option key={team.id} value={team.id}>{team.name}</option>
                            ))}
                          </select>
                        </td>
                        <td className="border border-gray-300 px-2 py-2">
                          <div className="flex space-x-1">
                            <button
                              onClick={() => saveEmployeeChanges(employee)}
                              disabled={loading}
                              className="px-2 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:bg-green-400"
                            >
                              <Save className="w-3 h-3" />
                            </button>
                            <button
                              onClick={cancelEdit}
                              className="px-2 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
                            >
                              <X className="w-3 h-3" />
                            </button>
                          </div>
                        </td>
                      </>
                    ) : (
                      // View Mode
                      <>
                        <td className="border border-gray-300 px-4 py-2">
                          {employee.first_name} {employee.last_name}
                        </td>
                        <td className="border border-gray-300 px-4 py-2">{employee.email}</td>
                        <td className="border border-gray-300 px-4 py-2">{employee.designation}</td>
                        <td className="border border-gray-300 px-4 py-2">
                          {(employee as any).teams?.name || 'No Team'}
                        </td>
                        <td className="border border-gray-300 px-4 py-2">
                          <div className="flex flex-col space-y-2">
                            <div className="flex flex-wrap gap-1">
                              <button
                                onClick={() => startEditEmployee(employee)}
                                className="px-2 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 flex items-center space-x-1"
                              >
                                <Edit className="w-3 h-3" />
                                <span>Edit</span>
                              </button>
                              <button
                                onClick={() => sendPasswordReset(employee.email)}
                                className="px-2 py-1 bg-orange-600 text-white rounded text-sm hover:bg-orange-700 flex items-center space-x-1"
                              >
                                <Key className="w-3 h-3" />
                                <span>Reset</span>
                              </button>
                              <button
                                onClick={() => setTempPassword(employee.email)}
                                className="px-2 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700 flex items-center space-x-1"
                              >
                                <Shield className="w-3 h-3" />
                                <span>Temp Pass</span>
                              </button>
                            </div>
                            <select
                              onChange={(e) => updateEmployeeTeam(employee.id, e.target.value)}
                              className="px-2 py-1 border border-gray-300 rounded text-sm"
                              defaultValue={employee.team_id || ''}
                            >
                              <option value="">No Team</option>
                              {teams.map(team => (
                                <option key={team.id} value={team.id}>{team.name}</option>
                              ))}
                            </select>
                          </div>
                        </td>
                      </>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {activeTab === 'sync' && (
        <AuthSyncManager />
      )}
    </div>
  );
};

export default UserManagementPanel;

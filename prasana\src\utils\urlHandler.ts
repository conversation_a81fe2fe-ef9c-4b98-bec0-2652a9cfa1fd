// Simple URL handling utilities for the application

export const getCurrentPath = (): string => {
  return window.location.pathname;
};

export const getSearchParams = (): URLSearchParams => {
  return new URLSearchParams(window.location.search);
};

export const navigateTo = (path: string): void => {
  window.history.pushState({}, '', path);
  window.dispatchEvent(new PopStateEvent('popstate'));
};

export const isResetPasswordPage = (): boolean => {
  return getCurrentPath() === '/reset-password';
};

export const hasResetToken = (): boolean => {
  const params = getSearchParams();
  return params.has('access_token') && params.has('type') && params.get('type') === 'recovery';
};

// Handle Supabase auth callback for password reset
export const handleAuthCallback = async () => {
  const params = getSearchParams();
  const accessToken = params.get('access_token');
  const refreshToken = params.get('refresh_token');
  const type = params.get('type');

  if (type === 'recovery' && accessToken && refreshToken) {
    // This is a password reset callback
    return {
      isPasswordReset: true,
      accessToken,
      refreshToken
    };
  }

  return {
    isPasswordReset: false
  };
};

import { useState, useEffect, useCallback, useMemo } from 'react';
import { roleManagementService } from '../services/roleManagementService';
import {
  Role,
  EmployeeRole,
  PermissionGroup,
  RoleTemplate,
  RoleApprovalRequest,
  RoleAuditLog,
  RoleFilters,
  EmployeeRoleFilters,
  AuditLogFilters,
  RoleStatistics,
  RoleHierarchy,
  PermissionMatrix
} from '../types/roleManagement';

interface UseRoleManagementOptions {
  autoLoad?: boolean;
  refreshInterval?: number;
  enableRealTimeUpdates?: boolean;
}

interface UseRoleManagementReturn {
  // Data
  roles: Role[];
  employeeRoles: EmployeeRole[];
  permissionGroups: PermissionGroup[];
  roleTemplates: RoleTemplate[];
  approvalRequests: RoleApprovalRequest[];
  auditLogs: RoleAuditLog[];
  statistics: RoleStatistics | null;
  roleHierarchy: RoleHierarchy[];
  
  // Loading states
  loading: boolean;
  loadingRoles: boolean;
  loadingAssignments: boolean;
  loadingApprovals: boolean;
  
  // Error states
  error: string | null;
  
  // Actions
  loadRoles: (filters?: RoleFilters) => Promise<void>;
  loadEmployeeRoles: (filters?: EmployeeRoleFilters) => Promise<void>;
  loadApprovalRequests: (status?: string) => Promise<void>;
  loadAuditLogs: (filters?: AuditLogFilters) => Promise<void>;
  loadStatistics: () => Promise<void>;
  loadRoleHierarchy: () => Promise<void>;
  
  createRole: (roleData: any) => Promise<Role>;
  updateRole: (roleId: string, updates: any) => Promise<Role>;
  deleteRole: (roleId: string) => Promise<void>;
  
  assignRole: (assignmentData: any) => Promise<EmployeeRole>;
  bulkAssignRoles: (bulkData: any) => Promise<void>;
  removeRole: (employeeRoleId: string, reason?: string) => Promise<void>;
  
  approveRequest: (requestId: string, comments?: string) => Promise<void>;
  rejectRequest: (requestId: string, reason: string) => Promise<void>;
  
  // Utility functions
  getUserRoles: (userId: string) => EmployeeRole[];
  getUserPermissions: (userId: string) => string[];
  hasPermission: (userId: string, permission: string) => boolean;
  getRolesByLevel: (level: number) => Role[];
  getExpiringRoles: (days: number) => EmployeeRole[];
  
  // Refresh functions
  refresh: () => Promise<void>;
  refreshRoles: () => Promise<void>;
  refreshAssignments: () => Promise<void>;
}

export const useRoleManagement = (
  currentUserId?: string,
  options: UseRoleManagementOptions = {}
): UseRoleManagementReturn => {
  const {
    autoLoad = true,
    refreshInterval = 0,
    enableRealTimeUpdates = false
  } = options;

  // State
  const [roles, setRoles] = useState<Role[]>([]);
  const [employeeRoles, setEmployeeRoles] = useState<EmployeeRole[]>([]);
  const [permissionGroups, setPermissionGroups] = useState<PermissionGroup[]>([]);
  const [roleTemplates, setRoleTemplates] = useState<RoleTemplate[]>([]);
  const [approvalRequests, setApprovalRequests] = useState<RoleApprovalRequest[]>([]);
  const [auditLogs, setAuditLogs] = useState<RoleAuditLog[]>([]);
  const [statistics, setStatistics] = useState<RoleStatistics | null>(null);
  const [roleHierarchy, setRoleHierarchy] = useState<RoleHierarchy[]>([]);

  // Loading states
  const [loading, setLoading] = useState(false);
  const [loadingRoles, setLoadingRoles] = useState(false);
  const [loadingAssignments, setLoadingAssignments] = useState(false);
  const [loadingApprovals, setLoadingApprovals] = useState(false);

  // Error state
  const [error, setError] = useState<string | null>(null);

  // Load functions
  const loadRoles = useCallback(async (filters?: RoleFilters) => {
    try {
      setLoadingRoles(true);
      setError(null);
      const data = await roleManagementService.getRoles(filters);
      setRoles(data);
    } catch (err) {
      setError((err as Error).message);
      console.error('Error loading roles:', err);
    } finally {
      setLoadingRoles(false);
    }
  }, []);

  const loadEmployeeRoles = useCallback(async (filters?: EmployeeRoleFilters) => {
    try {
      setLoadingAssignments(true);
      setError(null);
      const data = await roleManagementService.getEmployeeRoles(filters);
      setEmployeeRoles(data);
    } catch (err) {
      setError((err as Error).message);
      console.error('Error loading employee roles:', err);
    } finally {
      setLoadingAssignments(false);
    }
  }, []);

  const loadPermissionGroups = useCallback(async () => {
    try {
      const data = await roleManagementService.getPermissionGroups();
      setPermissionGroups(data);
    } catch (err) {
      console.error('Error loading permission groups:', err);
    }
  }, []);

  const loadRoleTemplates = useCallback(async (category?: string) => {
    try {
      const data = await roleManagementService.getRoleTemplates(category);
      setRoleTemplates(data);
    } catch (err) {
      console.error('Error loading role templates:', err);
    }
  }, []);

  const loadApprovalRequests = useCallback(async (status?: string) => {
    try {
      setLoadingApprovals(true);
      const data = await roleManagementService.getApprovalRequests(status);
      setApprovalRequests(data);
    } catch (err) {
      console.error('Error loading approval requests:', err);
    } finally {
      setLoadingApprovals(false);
    }
  }, []);

  const loadAuditLogs = useCallback(async (filters?: AuditLogFilters) => {
    try {
      const data = await roleManagementService.getAuditLogs(filters);
      setAuditLogs(data);
    } catch (err) {
      console.error('Error loading audit logs:', err);
    }
  }, []);

  const loadStatistics = useCallback(async () => {
    try {
      const data = await roleManagementService.getRoleStatistics();
      setStatistics(data);
    } catch (err) {
      console.error('Error loading statistics:', err);
    }
  }, []);

  const loadRoleHierarchy = useCallback(async () => {
    try {
      const data = await roleManagementService.getRoleHierarchy();
      setRoleHierarchy(data);
    } catch (err) {
      console.error('Error loading role hierarchy:', err);
    }
  }, []);

  // Action functions
  const createRole = useCallback(async (roleData: any): Promise<Role> => {
    try {
      const role = await roleManagementService.createRole(roleData, currentUserId);
      setRoles(prev => [...prev, role]);
      return role;
    } catch (err) {
      setError((err as Error).message);
      throw err;
    }
  }, [currentUserId]);

  const updateRole = useCallback(async (roleId: string, updates: any): Promise<Role> => {
    try {
      const role = await roleManagementService.updateRole(roleId, updates);
      setRoles(prev => prev.map(r => r.id === roleId ? role : r));
      return role;
    } catch (err) {
      setError((err as Error).message);
      throw err;
    }
  }, []);

  const deleteRole = useCallback(async (roleId: string): Promise<void> => {
    try {
      await roleManagementService.deleteRole(roleId);
      setRoles(prev => prev.filter(r => r.id !== roleId));
    } catch (err) {
      setError((err as Error).message);
      throw err;
    }
  }, []);

  const assignRole = useCallback(async (assignmentData: any): Promise<EmployeeRole> => {
    try {
      const assignment = await roleManagementService.assignRole(assignmentData, currentUserId || '');
      setEmployeeRoles(prev => [...prev, assignment]);
      return assignment;
    } catch (err) {
      setError((err as Error).message);
      throw err;
    }
  }, [currentUserId]);

  const bulkAssignRoles = useCallback(async (bulkData: any): Promise<void> => {
    try {
      await roleManagementService.bulkAssignRoles(bulkData, currentUserId || '');
      // Refresh employee roles after bulk assignment
      await loadEmployeeRoles();
    } catch (err) {
      setError((err as Error).message);
      throw err;
    }
  }, [currentUserId, loadEmployeeRoles]);

  const removeRole = useCallback(async (employeeRoleId: string, reason?: string): Promise<void> => {
    try {
      await roleManagementService.removeRole(employeeRoleId, currentUserId || '', reason);
      setEmployeeRoles(prev => prev.filter(er => er.id !== employeeRoleId));
    } catch (err) {
      setError((err as Error).message);
      throw err;
    }
  }, [currentUserId]);

  const approveRequest = useCallback(async (requestId: string, comments?: string): Promise<void> => {
    try {
      await roleManagementService.approveRequest(requestId, currentUserId || '', comments);
      setApprovalRequests(prev => 
        prev.map(req => req.id === requestId ? { ...req, status: 'approved' as const } : req)
      );
      // Refresh employee roles to show new assignments
      await loadEmployeeRoles();
    } catch (err) {
      setError((err as Error).message);
      throw err;
    }
  }, [currentUserId, loadEmployeeRoles]);

  const rejectRequest = useCallback(async (requestId: string, reason: string): Promise<void> => {
    try {
      await roleManagementService.rejectRequest(requestId, currentUserId || '', reason);
      setApprovalRequests(prev => 
        prev.map(req => req.id === requestId ? { ...req, status: 'rejected' as const } : req)
      );
    } catch (err) {
      setError((err as Error).message);
      throw err;
    }
  }, [currentUserId]);

  // Utility functions
  const getUserRoles = useCallback((userId: string): EmployeeRole[] => {
    return employeeRoles.filter(er => er.employee_id === userId && er.is_active);
  }, [employeeRoles]);

  const getUserPermissions = useCallback((userId: string): string[] => {
    const userRoles = getUserRoles(userId);
    const permissions = new Set<string>();
    
    userRoles.forEach(roleAssignment => {
      roleAssignment.role.permissions.forEach(permission => {
        permissions.add(permission);
      });
    });
    
    return Array.from(permissions);
  }, [getUserRoles]);

  const hasPermission = useCallback((userId: string, permission: string): boolean => {
    const userPermissions = getUserPermissions(userId);
    return userPermissions.includes(permission);
  }, [getUserPermissions]);

  const getRolesByLevel = useCallback((level: number): Role[] => {
    return roles.filter(role => role.role_level === level);
  }, [roles]);

  const getExpiringRoles = useCallback((days: number): EmployeeRole[] => {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() + days);
    
    return employeeRoles.filter(er => 
      er.expiry_date && 
      new Date(er.expiry_date) <= cutoffDate && 
      new Date(er.expiry_date) > new Date() &&
      er.is_active
    );
  }, [employeeRoles]);

  // Refresh functions
  const refresh = useCallback(async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadRoles(),
        loadEmployeeRoles(),
        loadPermissionGroups(),
        loadRoleTemplates(),
        loadStatistics(),
        loadRoleHierarchy()
      ]);
    } catch (err) {
      console.error('Error refreshing data:', err);
    } finally {
      setLoading(false);
    }
  }, [loadRoles, loadEmployeeRoles, loadPermissionGroups, loadRoleTemplates, loadStatistics, loadRoleHierarchy]);

  const refreshRoles = useCallback(async () => {
    await loadRoles();
  }, [loadRoles]);

  const refreshAssignments = useCallback(async () => {
    await loadEmployeeRoles();
  }, [loadEmployeeRoles]);

  // Auto-load data on mount
  useEffect(() => {
    if (autoLoad) {
      refresh();
    }
  }, [autoLoad, refresh]);

  // Set up refresh interval
  useEffect(() => {
    if (refreshInterval > 0) {
      const interval = setInterval(refresh, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [refreshInterval, refresh]);

  // Real-time updates (placeholder for future implementation)
  useEffect(() => {
    if (enableRealTimeUpdates) {
      // TODO: Implement real-time updates using Supabase subscriptions
      console.log('Real-time updates would be set up here');
    }
  }, [enableRealTimeUpdates]);

  return {
    // Data
    roles,
    employeeRoles,
    permissionGroups,
    roleTemplates,
    approvalRequests,
    auditLogs,
    statistics,
    roleHierarchy,
    
    // Loading states
    loading,
    loadingRoles,
    loadingAssignments,
    loadingApprovals,
    
    // Error state
    error,
    
    // Load functions
    loadRoles,
    loadEmployeeRoles,
    loadApprovalRequests,
    loadAuditLogs,
    loadStatistics,
    loadRoleHierarchy,
    
    // Action functions
    createRole,
    updateRole,
    deleteRole,
    assignRole,
    bulkAssignRoles,
    removeRole,
    approveRequest,
    rejectRequest,
    
    // Utility functions
    getUserRoles,
    getUserPermissions,
    hasPermission,
    getRolesByLevel,
    getExpiringRoles,
    
    // Refresh functions
    refresh,
    refreshRoles,
    refreshAssignments
  };
};

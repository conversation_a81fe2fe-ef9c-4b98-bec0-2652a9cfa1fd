# Profile Management System

## Overview

The HRMS now includes a comprehensive profile management system that allows users to manage their personal information with real-time synchronization to the HR dashboard. This system includes robust validation, security features, and audit logging.

## Features Implemented

### ✅ Enhanced User Profile Page
- **Complete Profile Display**: Shows all user and employee information in a clean, organized interface
- **Real-time Data Fetching**: Uses optimized database functions to retrieve combined user and employee data
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Role-based Access**: Users can edit their own profiles, managers can view their team members' profiles, super admins have full access

### ✅ Profile Editing Functionality
- **Inline Editing**: Toggle between view and edit modes with a single click
- **Comprehensive Fields**: Edit personal details, contact information, addresses, emergency contacts, and bio
- **Auto-save Validation**: Client-side validation before submission
- **Real-time Feedback**: Success and error messages with detailed validation feedback

### ✅ Profile Picture Upload
- **Drag & Drop Support**: Easy profile picture upload with visual feedback
- **File Validation**: Automatic validation of file type, size, and format
- **Storage Integration**: Uses Supabase Storage with automatic URL generation
- **Sync Across Tables**: Profile pictures are synchronized between users and employees tables

### ✅ Database Synchronization
- **Dual Table Updates**: Changes are automatically synchronized between `users` and `employees` tables
- **Database Triggers**: Automatic sync triggers ensure data consistency
- **Optimized Queries**: Uses database functions for efficient data retrieval
- **Transaction Safety**: All updates are performed atomically

### ✅ Security & Validation
- **Input Sanitization**: All user inputs are sanitized to prevent XSS attacks
- **Rate Limiting**: Prevents abuse with configurable rate limits
- **Access Control**: Role-based permissions with manager hierarchy support
- **Data Validation**: Comprehensive validation for all profile fields

### ✅ Audit Logging
- **Complete Audit Trail**: All profile changes are logged with timestamps
- **Security Events**: Suspicious activities and security violations are tracked
- **User Activity**: Track profile updates, picture uploads, and access attempts
- **Compliance Ready**: Audit logs support compliance and security requirements

## Database Schema

### New Functions Created
- `get_user_profile(user_uuid)`: Optimized function to retrieve complete user profile data
- `sync_user_to_employee()`: Trigger function to maintain data consistency

### Storage Setup
- **Avatars Bucket**: Created for profile picture storage with public access
- **File Validation**: Supports JPEG, PNG, WebP, and GIF formats up to 5MB

### Audit Logging
- Uses existing `audit_log` table to track all profile-related activities
- Includes IP addresses, user agents, and detailed change tracking

## API Endpoints

### Profile Service Functions
```typescript
// Fetch complete user profile
fetchUserProfile(userId: string): Promise<UserProfile | null>

// Update user profile with validation and audit logging
updateUserProfile(userId: string, updateData: ProfileUpdateData): Promise<{success: boolean; error?: string}>

// Upload profile picture with validation
uploadProfilePicture(userId: string, file: File): Promise<{success: boolean; url?: string; error?: string}>
```

### Validation Functions
```typescript
// Validate profile update data
validateProfileUpdate(data: ProfileUpdateData): ValidationResult

// Validate profile picture file
validateProfilePicture(file: File): ValidationResult

// Sanitize user input
sanitizeProfileUpdate(data: ProfileUpdateData): ProfileUpdateData
```

### Audit Functions
```typescript
// Log profile updates
logProfileUpdate(userId: string, action: string, tableName: string, recordId: string, oldValues?: any, newValues?: any)

// Check rate limits
checkRateLimit(userId: string, action: string, timeWindowMinutes: number, maxAttempts: number)

// Validate access permissions
validateProfileAccess(currentUserId: string, targetUserId: string, isSuperAdmin: boolean)
```

## Usage Examples

### Accessing User Profile
```typescript
// Navigate to user profile page
// URL: /profile/:userId or /profile (for current user)

// The page automatically:
// 1. Fetches complete profile data
// 2. Checks access permissions
// 3. Displays appropriate edit controls
```

### Editing Profile
```typescript
// Users can edit their own profiles
// Managers can view (but not edit) their team members' profiles
// Super admins can edit any profile

// Validation happens on:
// 1. Client-side before submission
// 2. Server-side with sanitization
// 3. Database level with constraints
```

### Profile Picture Upload
```typescript
// Drag and drop or click to upload
// Automatic validation for:
// - File type (images only)
// - File size (max 5MB)
// - Rate limiting (max 5 uploads per hour)
```

## Security Features

### Input Validation
- Email format validation
- Phone number format validation
- Date validation (birth dates cannot be in future)
- Text length limits
- XSS prevention through sanitization

### Rate Limiting
- Profile updates: 10 per hour
- Picture uploads: 5 per hour
- Configurable per action type

### Access Control
- Users can only edit their own profiles
- Managers can view their direct reports
- Super admins have full access
- All access attempts are logged

### Audit Trail
- Every profile change is logged
- Security events are tracked
- IP addresses and user agents recorded
- Compliance-ready audit logs

## HR Dashboard Integration

### Real-time Sync
- Profile changes immediately reflect in HR dashboard
- Database triggers ensure consistency
- No manual refresh required

### Enhanced Employee Profiles
- HR dashboard shows complete employee information
- Profile pictures display automatically
- All personal details available for HR review

## Configuration

### Environment Variables
```env
# Supabase configuration (already configured)
VITE_SUPABASE_URL=https://wamhcaexzqlrufqvbhdv.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key
```

### Rate Limiting Configuration
```typescript
// Configurable in auditService.ts
const RATE_LIMITS = {
  PROFILE_UPDATE: { window: 60, max: 10 }, // 10 updates per hour
  PICTURE_UPLOAD: { window: 60, max: 5 },  // 5 uploads per hour
};
```

## Testing

### Manual Testing
1. Start the development server: `npm run dev`
2. Navigate to `http://localhost:5173`
3. Log in with a user account
4. Navigate to profile page
5. Test editing various fields
6. Test profile picture upload
7. Verify changes appear in HR dashboard

### Validation Testing
- Try uploading large files (should be rejected)
- Try uploading non-image files (should be rejected)
- Try submitting invalid data (should show validation errors)
- Try rapid updates (should hit rate limits)

## Troubleshooting

### Common Issues
1. **Profile not loading**: Check database permissions and user authentication
2. **Picture upload failing**: Verify storage bucket exists and has correct permissions
3. **Validation errors**: Check input format and field requirements
4. **Rate limit errors**: Wait for rate limit window to reset

### Debug Information
- All errors are logged to browser console
- Audit logs provide detailed activity tracking
- Network tab shows API request/response details

## Future Enhancements

### Planned Features
- Bulk profile import/export
- Advanced search and filtering
- Profile completion tracking
- Integration with external HR systems
- Mobile app support

### Performance Optimizations
- Implement caching for frequently accessed profiles
- Add pagination for large employee lists
- Optimize image compression for profile pictures
- Add offline support for profile viewing

## Support

For technical support or feature requests, please contact the development team or create an issue in the project repository.

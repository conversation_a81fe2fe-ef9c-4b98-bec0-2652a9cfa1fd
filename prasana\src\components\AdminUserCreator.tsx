import React, { useState } from 'react';
import { UserPlus, AlertCircle, CheckCircle, Loader, Shield } from 'lucide-react';

const AdminUserCreator: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<{
    type: 'success' | 'error' | 'info';
    message: string;
    details?: any;
  } | null>(null);

  const createUserViaAPI = async () => {
    if (!email || !password) {
      setResult({
        type: 'error',
        message: 'Please provide both email and password'
      });
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      // Create user via Supabase Management API
      const response = await fetch('/api/admin/create-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          user_metadata: {
            full_name: fullName || email.split('@')[0],
          },
          email_confirm: true // Skip email confirmation
        })
      });

      const data = await response.json();

      if (!response.ok) {
        setResult({
          type: 'error',
          message: `API Error: ${data.message || 'Failed to create user'}`,
          details: data
        });
        return;
      }

      setResult({
        type: 'success',
        message: 'User created successfully via Management API!',
        details: data
      });

    } catch (err) {
      console.error('❌ Exception during API user creation:', err);
      setResult({
        type: 'error',
        message: `Exception: ${(err as Error).message}`,
        details: err
      });
    } finally {
      setLoading(false);
    }
  };

  const createUserDirectly = async () => {
    if (!email || !password) {
      setResult({
        type: 'error',
        message: 'Please provide both email and password'
      });
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      // This would require service role key - NOT recommended for client-side
      setResult({
        type: 'error',
        message: 'Direct user creation requires service role key and should be done server-side for security.',
        details: {
          recommendation: 'Use the signup form or create an admin API endpoint'
        }
      });

    } catch (err) {
      setResult({
        type: 'error',
        message: `Exception: ${(err as Error).message}`,
        details: err
      });
    } finally {
      setLoading(false);
    }
  };

  const generateRandomUser = () => {
    const randomId = Math.random().toString(36).substring(2, 8);
    setEmail(`test.user.${randomId}@technosprint.net`);
    setPassword('password123');
    setFullName(`Test User ${randomId.toUpperCase()}`);
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-purple-100 rounded-lg">
          <Shield className="w-6 h-6 text-purple-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Admin User Creator</h2>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <div className="flex items-start space-x-3">
          <AlertCircle className="w-5 h-5 text-yellow-500 mt-0.5" />
          <div>
            <p className="text-yellow-800 font-medium">Admin Tool</p>
            <p className="text-yellow-700 text-sm">
              This tool is for administrators to create user accounts. For regular user registration, 
              use the signup form instead.
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Email Address
          </label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            placeholder="<EMAIL>"
            disabled={loading}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Password
          </label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            placeholder="Enter password (min 6 chars)"
            disabled={loading}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Full Name
          </label>
          <input
            type="text"
            value={fullName}
            onChange={(e) => setFullName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            placeholder="User Full Name"
            disabled={loading}
          />
        </div>
      </div>

      <div className="flex space-x-2 mb-4">
        <button
          onClick={generateRandomUser}
          disabled={loading}
          className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 disabled:bg-gray-100"
        >
          Generate Random User
        </button>
      </div>

      <div className="flex space-x-4 mb-6">
        <button
          onClick={createUserViaAPI}
          disabled={loading}
          className="flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-purple-400"
        >
          {loading ? <Loader className="w-4 h-4 animate-spin" /> : <UserPlus className="w-4 h-4" />}
          <span>Create via API</span>
        </button>

        <button
          onClick={createUserDirectly}
          disabled={loading}
          className="flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:bg-gray-400"
        >
          {loading ? <Loader className="w-4 h-4 animate-spin" /> : <Shield className="w-4 h-4" />}
          <span>Direct Creation</span>
        </button>
      </div>

      {result && (
        <div className={`p-4 rounded-lg border ${
          result.type === 'success' ? 'bg-green-50 border-green-200' :
          result.type === 'error' ? 'bg-red-50 border-red-200' :
          'bg-blue-50 border-blue-200'
        }`}>
          <div className="flex items-start space-x-3">
            {result.type === 'success' && <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />}
            {result.type === 'error' && <AlertCircle className="w-5 h-5 text-red-500 mt-0.5" />}
            {result.type === 'info' && <AlertCircle className="w-5 h-5 text-blue-500 mt-0.5" />}
            <div className="flex-1">
              <p className={`font-medium ${
                result.type === 'success' ? 'text-green-800' :
                result.type === 'error' ? 'text-red-800' :
                'text-blue-800'
              }`}>
                {result.message}
              </p>
              {result.details && (
                <details className="mt-2">
                  <summary className="cursor-pointer text-sm text-gray-600">
                    View Details
                  </summary>
                  <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                    {JSON.stringify(result.details, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          </div>
        </div>
      )}

      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-medium text-gray-900 mb-2">Troubleshooting Tips:</h3>
        <ul className="text-sm text-gray-700 space-y-1">
          <li>• Check if email confirmation is required in Supabase Auth settings</li>
          <li>• Verify that signup is enabled in your Supabase project</li>
          <li>• Ensure your email domain is not blocked</li>
          <li>• Check if you have proper RLS policies for the users table</li>
          <li>• Try using the UserCreationTester component first</li>
        </ul>
      </div>
    </div>
  );
};

export default AdminUserCreator;

import React, { useState, useEffect } from 'react';
import { 
  Shield, 
  User, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  RefreshCw,
  Database,
  Key,
  Users
} from 'lucide-react';
import { useHRMS } from '../../contexts/HRMSContext';
import { supabase } from '../../supabaseClient';

interface AuthStatus {
  isAuthenticated: boolean;
  userId: string | null;
  userEmail: string | null;
  hasEmployeeRecord: boolean;
  employeeId: string | null;
  hasRoleAssignments: boolean;
  roleCount: number;
  canAccessRoles: boolean;
  canCreateRoles: boolean;
  lastChecked: Date;
}

const AuthenticationTester: React.FC = () => {
  const { currentEmployee } = useHRMS();
  const [authStatus, setAuthStatus] = useState<AuthStatus | null>(null);
  const [testing, setTesting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    runAuthTest();
  }, []);

  const runAuthTest = async () => {
    setTesting(true);
    setError(null);

    try {
      // Test 1: Check Supabase authentication
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError) {
        throw new Error(`Authentication error: ${authError.message}`);
      }

      const isAuthenticated = !!user;
      const userId = user?.id || null;
      const userEmail = user?.email || null;

      // Test 2: Check employee record
      let hasEmployeeRecord = false;
      let employeeId = null;
      
      if (userId) {
        const { data: employee, error: empError } = await supabase
          .from('employees')
          .select('id, first_name, last_name, email')
          .eq('user_id', userId)
          .single();

        if (!empError && employee) {
          hasEmployeeRecord = true;
          employeeId = employee.id;
        }
      }

      // Test 3: Check role assignments
      let hasRoleAssignments = false;
      let roleCount = 0;

      if (employeeId) {
        const { data: roles, error: roleError } = await supabase
          .from('employee_roles')
          .select('id, role:roles(name)')
          .eq('employee_id', employeeId)
          .eq('is_active', true);

        if (!roleError && roles) {
          hasRoleAssignments = roles.length > 0;
          roleCount = roles.length;
        }
      }

      // Test 4: Check roles table access
      let canAccessRoles = false;
      try {
        const { data, error } = await supabase
          .from('roles')
          .select('id')
          .limit(1);
        
        canAccessRoles = !error;
      } catch (err) {
        console.warn('Cannot access roles table:', err);
      }

      // Test 5: Check role creation capability
      let canCreateRoles = false;
      try {
        // Try to insert a test role (will be rolled back)
        const { error } = await supabase
          .from('roles')
          .insert([{
            name: '__TEST_ROLE_DELETE_ME__',
            description: 'Test role',
            permissions: ['employee_access'],
            role_level: 1,
            is_active: false
          }]);

        if (!error) {
          canCreateRoles = true;
          // Clean up test role
          await supabase
            .from('roles')
            .delete()
            .eq('name', '__TEST_ROLE_DELETE_ME__');
        }
      } catch (err) {
        console.warn('Cannot create roles:', err);
      }

      setAuthStatus({
        isAuthenticated,
        userId,
        userEmail,
        hasEmployeeRecord,
        employeeId,
        hasRoleAssignments,
        roleCount,
        canAccessRoles,
        canCreateRoles,
        lastChecked: new Date()
      });

    } catch (err) {
      setError((err as Error).message);
    } finally {
      setTesting(false);
    }
  };

  const createEmployeeRecord = async () => {
    if (!authStatus?.userId) return;

    try {
      setTesting(true);
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) throw new Error('No authenticated user found');

      const { error } = await supabase
        .from('employees')
        .insert([{
          user_id: user.id,
          first_name: user.user_metadata?.first_name || 'User',
          last_name: user.user_metadata?.last_name || 'Name',
          email: user.email || '<EMAIL>',
          designation: 'Employee',
          department: 'General',
          status: 'active',
          joining_date: new Date().toISOString().split('T')[0]
        }]);

      if (error) throw error;

      // Refresh auth status
      await runAuthTest();
    } catch (err) {
      setError((err as Error).message);
    } finally {
      setTesting(false);
    }
  };

  const assignBasicRole = async () => {
    if (!authStatus?.employeeId) return;

    try {
      setTesting(true);
      
      // Get Employee role
      const { data: role, error: roleError } = await supabase
        .from('roles')
        .select('id')
        .eq('name', 'Employee')
        .single();

      if (roleError || !role) throw new Error('Employee role not found');

      const { error } = await supabase
        .from('employee_roles')
        .insert([{
          employee_id: authStatus.employeeId,
          role_id: role.id,
          assigned_by: authStatus.employeeId,
          approval_status: 'approved'
        }]);

      if (error) throw error;

      // Refresh auth status
      await runAuthTest();
    } catch (err) {
      setError((err as Error).message);
    } finally {
      setTesting(false);
    }
  };

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircle className="w-5 h-5 text-green-600" />
    ) : (
      <XCircle className="w-5 h-5 text-red-600" />
    );
  };

  const getStatusColor = (status: boolean) => {
    return status ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Authentication Tester</h1>
            <p className="text-gray-600">Diagnose authentication and database access issues</p>
          </div>
          <button
            onClick={runAuthTest}
            disabled={testing}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
          >
            <RefreshCw className={`w-4 h-4 ${testing ? 'animate-spin' : ''}`} />
            <span>{testing ? 'Testing...' : 'Run Test'}</span>
          </button>
        </div>

        {/* Current Employee Info */}
        {currentEmployee && (
          <div className="bg-blue-50 rounded-lg p-4">
            <h3 className="font-medium text-blue-900 mb-2">Current Employee Context</h3>
            <p className="text-blue-800">
              {currentEmployee.first_name} {currentEmployee.last_name} ({currentEmployee.email})
            </p>
            <p className="text-blue-600 text-sm">ID: {currentEmployee.id}</p>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <span className="text-red-800 font-medium">Error</span>
          </div>
          <p className="text-red-600 mt-2">{error}</p>
        </div>
      )}

      {/* Test Results */}
      {authStatus && (
        <div className="space-y-4">
          {/* Authentication Status */}
          <div className={`border rounded-lg p-4 ${getStatusColor(authStatus.isAuthenticated)}`}>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <Key className="w-5 h-5" />
                <h3 className="font-medium">Supabase Authentication</h3>
              </div>
              {getStatusIcon(authStatus.isAuthenticated)}
            </div>
            <div className="text-sm space-y-1">
              <p><strong>Status:</strong> {authStatus.isAuthenticated ? 'Authenticated' : 'Not Authenticated'}</p>
              {authStatus.userId && <p><strong>User ID:</strong> {authStatus.userId}</p>}
              {authStatus.userEmail && <p><strong>Email:</strong> {authStatus.userEmail}</p>}
            </div>
          </div>

          {/* Employee Record */}
          <div className={`border rounded-lg p-4 ${getStatusColor(authStatus.hasEmployeeRecord)}`}>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <User className="w-5 h-5" />
                <h3 className="font-medium">Employee Record</h3>
              </div>
              {getStatusIcon(authStatus.hasEmployeeRecord)}
            </div>
            <div className="text-sm space-y-1">
              <p><strong>Status:</strong> {authStatus.hasEmployeeRecord ? 'Record Found' : 'No Record'}</p>
              {authStatus.employeeId && <p><strong>PS Number:</strong> {authStatus.employeeId}</p>}
            </div>
            {!authStatus.hasEmployeeRecord && authStatus.isAuthenticated && (
              <button
                onClick={createEmployeeRecord}
                disabled={testing}
                className="mt-2 bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm"
              >
                Create Employee Record
              </button>
            )}
          </div>

          {/* Role Assignments */}
          <div className={`border rounded-lg p-4 ${getStatusColor(authStatus.hasRoleAssignments)}`}>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <Users className="w-5 h-5" />
                <h3 className="font-medium">Role Assignments</h3>
              </div>
              {getStatusIcon(authStatus.hasRoleAssignments)}
            </div>
            <div className="text-sm space-y-1">
              <p><strong>Status:</strong> {authStatus.hasRoleAssignments ? `${authStatus.roleCount} roles assigned` : 'No roles assigned'}</p>
            </div>
            {!authStatus.hasRoleAssignments && authStatus.hasEmployeeRecord && (
              <button
                onClick={assignBasicRole}
                disabled={testing}
                className="mt-2 bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm"
              >
                Assign Employee Role
              </button>
            )}
          </div>

          {/* Database Access */}
          <div className={`border rounded-lg p-4 ${getStatusColor(authStatus.canAccessRoles)}`}>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <Database className="w-5 h-5" />
                <h3 className="font-medium">Database Access</h3>
              </div>
              {getStatusIcon(authStatus.canAccessRoles)}
            </div>
            <div className="text-sm space-y-1">
              <p><strong>Read Access:</strong> {authStatus.canAccessRoles ? 'Working' : 'Failed'}</p>
              <p><strong>Write Access:</strong> {authStatus.canCreateRoles ? 'Working' : 'Failed'}</p>
            </div>
          </div>

          {/* Test Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-2">Test Summary</h3>
            <div className="text-sm text-gray-600">
              <p>Last checked: {authStatus.lastChecked.toLocaleString()}</p>
              <p>Overall status: {
                authStatus.isAuthenticated && authStatus.hasEmployeeRecord && authStatus.hasRoleAssignments && authStatus.canAccessRoles
                  ? '✅ All systems working'
                  : '⚠️ Issues detected'
              }</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AuthenticationTester;

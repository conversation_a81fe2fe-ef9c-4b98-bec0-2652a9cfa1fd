import { supabase } from '../supabaseClient';

// Create complete organizational hierarchy from scratch
export async function createOrganizationalHierarchy() {
  try {
    // Step 1: Clear existing employees (except system users)
    await supabase
      .from('employees')
      .delete()
      .neq('employee_id', 'SYSTEM_ADMIN');

    // Step 2: Create CEO - ARUN G
    const { data: ceoData, error: ceoError } = await supabase
      .from('employees')
      .insert([{
        user_id: crypto.randomUUID(),
        first_name: 'ARUN',
        last_name: 'G',
        designation: 'CEO',
        department: 'EXECUTIVE',
        employee_id: 'EMP_CEO_001',
        employee_code: 'CEO001',
        email: '<EMAIL>',
        status: 'active',
        reports_to: null,
        hire_date: '2020-01-01',
        joining_date: '2020-01-01',
        profile_picture: '/profiles/arun.png'
      }])
      .select()
      .single();

    if (ceoError) throw new Error('Failed to create CEO');
    const ceoId = ceoData.id;

    // Step 3: Create CRO - Priyadarshini
    const { data: croData, error: croError } = await supabase
      .from('employees')
      .insert([{
        user_id: crypto.randomUUID(),
        first_name: 'Priyadarshini',
        last_name: '',
        designation: 'Chief Revenue Officer',
        department: 'REVENUE',
        employee_id: 'EMP_CRO_001',
        employee_code: 'CRO001',
        email: '<EMAIL>',
        status: 'active',
        reports_to: ceoId,
        hire_date: '2023-01-01',
        joining_date: '2023-01-01'
      }])
      .select()
      .single();

    if (croError) throw new Error('Failed to create CRO');
    const croId = croData.id;

    // Step 4: Create SDMs
    const sdmData = [
      { name: 'Eashwara Prasadh', dept: 'NEXUS', id: 'EMP_SDM_001' },
      { name: 'Yuvaraj S', dept: 'DYNAMIX', id: 'EMP_SDM_002' },
      { name: 'Aamina Begam T', dept: 'TITAN', id: 'EMP_SDM_003' },
      { name: 'Sri Ram', dept: 'ATHENA', id: 'EMP_SDM_004' }
    ];

    const sdmInserts = sdmData.map(sdm => ({
      user_id: crypto.randomUUID(),
      first_name: sdm.name.split(' ')[0],
      last_name: sdm.name.split(' ').slice(1).join(' '),
      designation: 'Service Delivery Manager',
      department: sdm.dept,
      employee_id: sdm.id,
      employee_code: sdm.id.replace('EMP_', ''),
      email: `${sdm.name.toLowerCase().replace(/\s+/g, '.')}@technosprint.net`,
      status: 'active',
      reports_to: croId,
      hire_date: '2023-01-01',
      joining_date: '2023-01-01'
    }));

    const { data: sdmResults, error: sdmError } = await supabase
      .from('employees')
      .insert(sdmInserts)
      .select();

    if (sdmError) throw new Error('Failed to create SDMs');

    // Create SDM mapping
    const sdmMap: Record<string, string> = {};
    sdmResults.forEach(sdm => {
      sdmMap[sdm.department] = sdm.id;
    });

    // Step 5: Create TDMs and CXMs
    const managementData = [
      { name: 'Yusuf Fayas', role: 'Technical Delivery Manager', dept: 'NEXUS' },
      { name: 'Darshan K', role: 'Client Experience Manager', dept: 'NEXUS' },
      { name: 'Purushoth', role: 'Technical Delivery Manager', dept: 'DYNAMIX' },
      { name: 'Kiyshore K', role: 'Client Experience Manager', dept: 'DYNAMIX' },
      { name: 'Gowtham Kollati', role: 'Technical Delivery Manager', dept: 'TITAN' },
      { name: 'Prasanna', role: 'Client Experience Manager', dept: 'TITAN' },
      { name: 'Selvendrane', role: 'Technical Delivery Manager', dept: 'ATHENA' },
      { name: 'Maheshwaran', role: 'Client Experience Manager', dept: 'ATHENA' }
    ];

    const mgmtInserts = managementData.map((mgmt, index) => ({
      user_id: crypto.randomUUID(),
      first_name: mgmt.name.split(' ')[0],
      last_name: mgmt.name.split(' ').slice(1).join(' ') || '',
      designation: mgmt.role,
      department: mgmt.dept,
      employee_id: `EMP_MGT_${String(index + 1).padStart(3, '0')}`,
      employee_code: `MGT${String(index + 1).padStart(3, '0')}`,
      email: `${mgmt.name.toLowerCase().replace(/\s+/g, '.')}@technosprint.net`,
      status: 'active',
      reports_to: sdmMap[mgmt.dept],
      hire_date: '2023-06-01',
      joining_date: '2023-06-01'
    }));

    const { data: mgmtResults, error: mgmtError } = await supabase
      .from('employees')
      .insert(mgmtInserts)
      .select();

    if (mgmtError) throw new Error('Failed to create management team');

    // Create TDM mapping
    const tdmMap: Record<string, string> = {};
    mgmtResults
      .filter(m => m.designation === 'Technical Delivery Manager')
      .forEach(tdm => {
        tdmMap[tdm.department] = tdm.id;
      });

    // Step 6: Create Team Members
    const teamData = [
      { name: 'Gaushik', dept: 'NEXUS' },
      { name: 'Hariharan', dept: 'NEXUS' },
      { name: 'Praveen Dommeti', dept: 'DYNAMIX' },
      { name: 'Nithish', dept: 'DYNAMIX' },
      { name: 'Sivaranjani K', dept: 'TITAN' },
      { name: 'Fazeela M', dept: 'TITAN' },
      { name: 'Keerthipriya', dept: 'TITAN' },
      { name: 'Kollati Gowtham', dept: 'ATHENA' },
      { name: 'Shri Mathi', dept: 'ATHENA' }
    ];

    const teamInserts = teamData.map((member, index) => ({
      user_id: crypto.randomUUID(),
      first_name: member.name.split(' ')[0],
      last_name: member.name.split(' ').slice(1).join(' ') || '',
      designation: 'Associate Trainee',
      department: member.dept,
      employee_id: `EMP_TM_${String(index + 1).padStart(3, '0')}`,
      employee_code: `TM${String(index + 1).padStart(3, '0')}`,
      email: `${member.name.toLowerCase().replace(/\s+/g, '.')}@technosprint.net`,
      status: 'active',
      reports_to: tdmMap[member.dept],
      hire_date: '2024-01-01',
      joining_date: '2024-01-01'
    }));

    const { data: teamResults, error: teamError } = await supabase
      .from('employees')
      .insert(teamInserts)
      .select();

    if (teamError) throw new Error('Failed to create team members');

    return {
      success: true,
      message: 'Organizational hierarchy created successfully',
      counts: {
        ceo: 1,
        cro: 1,
        sdm: sdmResults.length,
        management: mgmtResults.length,
        team: teamResults.length,
        total: 1 + 1 + sdmResults.length + mgmtResults.length + teamResults.length
      }
    };

  } catch (error) {
    throw new Error(`Failed to create hierarchy: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Function to verify and debug hierarchy
export async function debugHierarchy() {
  try {
    const { data: employees, error } = await supabase
      .from('employees')
      .select('*')
      .eq('status', 'active')
      .order('designation', { ascending: false });

    if (error) throw error;

    console.log('=== HIERARCHY DEBUG ===');
    console.log(`Total employees: ${employees?.length || 0}`);

    if (employees) {
      const levels = {
        'CEO': employees.filter(e => e.designation === 'CEO'),
        'Chief Revenue Officer': employees.filter(e => e.designation === 'Chief Revenue Officer'),
        'Service Delivery Manager': employees.filter(e => e.designation === 'Service Delivery Manager'),
        'Technical Delivery Manager': employees.filter(e => e.designation === 'Technical Delivery Manager'),
        'Client Experience Manager': employees.filter(e => e.designation === 'Client Experience Manager'),
        'Associate Trainee': employees.filter(e => e.designation === 'Associate Trainee')
      };

      Object.entries(levels).forEach(([level, emps]) => {
        console.log(`\n${level} (${emps.length}):`);
        emps.forEach(emp => {
          console.log(`  • ${emp.first_name} ${emp.last_name} (${emp.department}) - Reports to: ${emp.reports_to || 'None'}`);
        });
      });
    }

    return { success: true, employees };
  } catch (error) {
    console.error('Debug failed:', error);
    return { success: false, error };
  }
}

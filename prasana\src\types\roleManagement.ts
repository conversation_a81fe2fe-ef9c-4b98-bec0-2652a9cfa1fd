// Enhanced Role Management Types

export interface PermissionGroup {
  id: string;
  name: string;
  description: string;
  category: string;
  permissions: string[];
  color: string;
  icon: string;
  sort_order: number;
  is_active: boolean;
  created_at: string;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  parent_role_id?: string;
  role_level: number;
  is_active: boolean;
  is_template: boolean;
  template_category?: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
  // Computed fields
  parent_role?: Role;
  child_roles?: Role[];
  permission_groups?: PermissionGroup[];
}

export interface RoleTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  permission_groups: string[];
  permissions: string[];
  suggested_level: number;
  is_active: boolean;
  usage_count: number;
  created_by?: string;
  created_at: string;
}

export interface EmployeeRole {
  id: string;
  employee_id: string;
  role_id: string;
  assigned_by?: string;
  assigned_date: string;
  expiry_date?: string;
  approval_status: 'pending' | 'approved' | 'rejected';
  approved_by?: string;
  approved_date?: string;
  rejection_reason?: string;
  auto_assigned: boolean;
  is_active: boolean;
  updated_at: string;
  // Relations
  role: Role;
  employee: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    designation: string;
    department: string;
  };
  assigned_by_employee?: {
    first_name: string;
    last_name: string;
  };
}

export interface RoleApprovalRequest {
  id: string;
  employee_id: string;
  role_id: string;
  requested_by: string;
  approver_id?: string;
  request_type: 'assign' | 'remove' | 'modify';
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  justification?: string;
  approver_comments?: string;
  expiry_date?: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  requested_date: string;
  processed_date?: string;
  created_at: string;
  // Relations
  employee: {
    first_name: string;
    last_name: string;
    email: string;
    designation: string;
  };
  role: Role;
  requester: {
    first_name: string;
    last_name: string;
  };
  approver?: {
    first_name: string;
    last_name: string;
  };
}

export interface RoleAuditLog {
  id: string;
  employee_id?: string;
  role_id?: string;
  action: 'assigned' | 'removed' | 'modified' | 'expired' | 'created' | 'deleted';
  performed_by?: string;
  old_values?: any;
  new_values?: any;
  reason?: string;
  ip_address?: string;
  user_agent?: string;
  session_id?: string;
  created_at: string;
  // Relations
  employee?: {
    first_name: string;
    last_name: string;
  };
  role?: {
    name: string;
  };
  performer?: {
    first_name: string;
    last_name: string;
  };
}

export interface BulkRoleOperation {
  id: string;
  operation_type: 'bulk_assign' | 'bulk_remove' | 'bulk_modify';
  performed_by: string;
  total_records: number;
  successful_records: number;
  failed_records: number;
  operation_data: any;
  status: 'in_progress' | 'completed' | 'failed' | 'cancelled';
  started_at: string;
  completed_at?: string;
  error_details?: string;
  // Relations
  performer: {
    first_name: string;
    last_name: string;
  };
}

// Form interfaces
export interface RoleFormData {
  name: string;
  description: string;
  permissions: string[];
  permission_groups: string[];
  parent_role_id?: string;
  role_level: number;
  template_category?: string;
}

export interface BulkAssignmentData {
  employee_ids: string[];
  role_id: string;
  expiry_date?: string;
  justification?: string;
  require_approval: boolean;
}

export interface RoleAssignmentFormData {
  employee_id: string;
  role_id: string;
  expiry_date?: string;
  justification?: string;
  require_approval: boolean;
  priority: 'low' | 'normal' | 'high' | 'urgent';
}

// Filter and search interfaces
export interface RoleFilters {
  search: string;
  category: string;
  level: number | null;
  status: 'active' | 'inactive' | 'all';
  permission_group: string;
  has_expiry: boolean | null;
  approval_status: string;
}

export interface EmployeeRoleFilters {
  search: string;
  role_id: string;
  department: string;
  approval_status: string;
  expiry_status: 'active' | 'expiring_soon' | 'expired' | 'all';
  assigned_date_from?: string;
  assigned_date_to?: string;
}

export interface AuditLogFilters {
  search: string;
  action: string;
  date_from?: string;
  date_to?: string;
  employee_id: string;
  role_id: string;
}

// Utility types
export interface RoleHierarchy {
  role: Role;
  level: number;
  children: RoleHierarchy[];
  inherited_permissions: string[];
}

export interface PermissionMatrix {
  employee_id: string;
  employee_name: string;
  roles: {
    role_id: string;
    role_name: string;
    permissions: string[];
    expiry_date?: string;
  }[];
  effective_permissions: string[];
}

export interface RoleStatistics {
  total_roles: number;
  active_roles: number;
  total_assignments: number;
  pending_approvals: number;
  expiring_soon: number;
  role_distribution: {
    role_name: string;
    assignment_count: number;
  }[];
  permission_usage: {
    permission: string;
    usage_count: number;
  }[];
}

// API response types
export interface RoleManagementResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  has_more: boolean;
}

// Constants
export const ROLE_LEVELS = {
  EMPLOYEE: 1,
  TEAM_LEAD: 2,
  MANAGER: 3,
  SENIOR_MANAGER: 4,
  EXECUTIVE: 5
} as const;

export const APPROVAL_PRIORITIES = {
  LOW: 'low',
  NORMAL: 'normal',
  HIGH: 'high',
  URGENT: 'urgent'
} as const;

export const ROLE_CATEGORIES = {
  EMPLOYEE: 'Employee',
  LEADERSHIP: 'Leadership',
  MANAGEMENT: 'Management',
  HR: 'HR',
  FINANCE: 'Finance',
  ADMIN: 'Admin'
} as const;

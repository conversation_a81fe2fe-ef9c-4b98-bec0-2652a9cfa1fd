import React, { useState, useEffect } from 'react';
import { Refresh<PERSON>w, AlertTriangle, CheckCircle, Users, Database, RotateCcw } from 'lucide-react';
import { supabase } from '../supabaseClient';

interface AuthUser {
  id: string;
  email: string;
  created_at: string;
  email_confirmed_at: string | null;
}

interface Employee {
  id: string;
  user_id: string | null;
  first_name: string;
  last_name: string;
  email: string;
  status: string;
}

interface SyncIssue {
  type: 'missing_employee' | 'missing_auth' | 'email_mismatch' | 'orphaned_employee';
  authUser?: AuthUser;
  employee?: Employee;
  description: string;
}

const AuthSyncManager: React.FC = () => {
  const [authUsers, setAuthUsers] = useState<AuthUser[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [syncIssues, setSyncIssues] = useState<SyncIssue[]>([]);
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [result, setResult] = useState<{
    type: 'success' | 'error' | 'info';
    message: string;
  } | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // Load auth users
      const { data: authData, error: authError } = await supabase
        .from('auth.users')
        .select('id, email, created_at, email_confirmed_at');

      if (authError) throw authError;

      // Load employees
      const { data: employeeData, error: employeeError } = await supabase
        .from('employees')
        .select('id, user_id, first_name, last_name, email, status')
        .eq('status', 'active')
        .neq('designation', 'System Administrator');

      if (employeeError) throw employeeError;

      setAuthUsers(authData || []);
      setEmployees(employeeData || []);
      
      // Analyze sync issues
      analyzeSyncIssues(authData || [], employeeData || []);

    } catch (error: any) {
      setResult({
        type: 'error',
        message: `Error loading data: ${error.message}`
      });
    } finally {
      setLoading(false);
    }
  };

  const analyzeSyncIssues = (authUsers: AuthUser[], employees: Employee[]) => {
    const issues: SyncIssue[] = [];

    // Check for auth users without employee records
    authUsers.forEach(authUser => {
      const matchingEmployee = employees.find(emp => emp.user_id === authUser.id);
      if (!matchingEmployee) {
        issues.push({
          type: 'missing_employee',
          authUser,
          description: `Auth user ${authUser.email} has no corresponding employee record`
        });
      }
    });

    // Check for employees without auth users
    employees.forEach(employee => {
      if (employee.user_id) {
        const matchingAuth = authUsers.find(auth => auth.id === employee.user_id);
        if (!matchingAuth) {
          issues.push({
            type: 'missing_auth',
            employee,
            description: `Employee ${employee.first_name} ${employee.last_name} has invalid user_id reference`
          });
        }
      } else {
        issues.push({
          type: 'orphaned_employee',
          employee,
          description: `Employee ${employee.first_name} ${employee.last_name} has no auth user connection`
        });
      }
    });

    // Check for email mismatches
    employees.forEach(employee => {
      if (employee.user_id) {
        const matchingAuth = authUsers.find(auth => auth.id === employee.user_id);
        if (matchingAuth && matchingAuth.email !== employee.email) {
          issues.push({
            type: 'email_mismatch',
            authUser: matchingAuth,
            employee,
            description: `Email mismatch: Auth(${matchingAuth.email}) vs Employee(${employee.email})`
          });
        }
      }
    });

    setSyncIssues(issues);
  };

  const syncAllUsers = async () => {
    setSyncing(true);
    setResult(null);
    
    try {
      let fixedCount = 0;

      for (const issue of syncIssues) {
        switch (issue.type) {
          case 'email_mismatch':
            if (issue.employee && issue.authUser) {
              // Update employee email to match auth
              await supabase
                .from('employees')
                .update({ email: issue.authUser.email })
                .eq('id', issue.employee.id);
              fixedCount++;
            }
            break;

          case 'missing_employee':
            if (issue.authUser) {
              // Create employee record for auth user
              const nameParts = issue.authUser.email.split('@')[0].split('.');
              await supabase
                .from('employees')
                .insert({
                  user_id: issue.authUser.id,
                  employee_id: `EMP_${Date.now()}`,
                  first_name: nameParts[0] || 'User',
                  last_name: nameParts[1] || 'Name',
                  email: issue.authUser.email,
                  designation: 'Associate Trainee',
                  department: 'General',
                  status: 'active',
                  joining_date: new Date().toISOString().split('T')[0]
                });
              fixedCount++;
            }
            break;

          case 'missing_auth':
            if (issue.employee) {
              // Clear invalid user_id reference
              await supabase
                .from('employees')
                .update({ user_id: null })
                .eq('id', issue.employee.id);
              fixedCount++;
            }
            break;

          case 'orphaned_employee':
            // These need manual auth user creation
            break;
        }
      }

      setResult({
        type: 'success',
        message: `Sync completed! Fixed ${fixedCount} issues. Reload to see updated status.`
      });

      // Reload data to show updated status
      setTimeout(() => {
        loadData();
      }, 1000);

    } catch (error: any) {
      setResult({
        type: 'error',
        message: `Sync error: ${error.message}`
      });
    } finally {
      setSyncing(false);
    }
  };

  const createAuthUserForEmployee = async (employee: Employee) => {
    try {
      // Create auth user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: employee.email,
        password: 'TempPassword123!', // Temporary password
        options: {
          data: {
            full_name: `${employee.first_name} ${employee.last_name}`.trim()
          }
        }
      });

      if (authError) throw authError;

      // Update employee with user_id
      if (authData.user) {
        await supabase
          .from('employees')
          .update({ user_id: authData.user.id })
          .eq('id', employee.id);

        setResult({
          type: 'success',
          message: `Auth user created for ${employee.first_name} ${employee.last_name}. Temporary password: TempPassword123!`
        });

        loadData(); // Reload to update display
      }

    } catch (error: any) {
      setResult({
        type: 'error',
        message: `Error creating auth user: ${error.message}`
      });
    }
  };

  const getIssueColor = (type: string) => {
    switch (type) {
      case 'email_mismatch': return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'missing_employee': return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'missing_auth': return 'bg-red-50 border-red-200 text-red-800';
      case 'orphaned_employee': return 'bg-orange-50 border-orange-200 text-orange-800';
      default: return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-100 rounded-lg">
            <RotateCcw className="w-6 h-6 text-green-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900">Auth System Sync Manager</h2>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={loadData}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-blue-400 flex items-center space-x-2"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
          
          <button
            onClick={syncAllUsers}
            disabled={syncing || syncIssues.length === 0}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-green-400 flex items-center space-x-2"
          >
            <RotateCcw className={`w-4 h-4 ${syncing ? 'animate-spin' : ''}`} />
            <span>Auto-Fix Issues</span>
          </button>
        </div>
      </div>

      {/* Result Message */}
      {result && (
        <div className={`mb-6 p-4 rounded-lg border ${
          result.type === 'success' 
            ? 'bg-green-50 border-green-200 text-green-800'
            : result.type === 'error'
            ? 'bg-red-50 border-red-200 text-red-800'
            : 'bg-blue-50 border-blue-200 text-blue-800'
        }`}>
          <div className="flex items-center space-x-2">
            {result.type === 'success' ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <AlertTriangle className="w-5 h-5" />
            )}
            <span>{result.message}</span>
          </div>
        </div>
      )}

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-center space-x-2">
            <Users className="w-5 h-5 text-blue-600" />
            <span className="font-medium text-blue-900">Auth Users</span>
          </div>
          <p className="text-2xl font-bold text-blue-600">{authUsers.length}</p>
        </div>
        
        <div className="bg-green-50 p-4 rounded-lg">
          <div className="flex items-center space-x-2">
            <Database className="w-5 h-5 text-green-600" />
            <span className="font-medium text-green-900">Employees</span>
          </div>
          <p className="text-2xl font-bold text-green-600">{employees.length}</p>
        </div>
        
        <div className="bg-red-50 p-4 rounded-lg">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <span className="font-medium text-red-900">Sync Issues</span>
          </div>
          <p className="text-2xl font-bold text-red-600">{syncIssues.length}</p>
        </div>
      </div>

      {/* Sync Issues */}
      {syncIssues.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Sync Issues Found</h3>
          
          {syncIssues.map((issue, index) => (
            <div key={index} className={`p-4 rounded-lg border ${getIssueColor(issue.type)}`}>
              <div className="flex items-start justify-between">
                <div>
                  <h4 className="font-medium capitalize">
                    {issue.type.replace('_', ' ')}
                  </h4>
                  <p className="text-sm mt-1">{issue.description}</p>
                  
                  {issue.authUser && (
                    <p className="text-xs mt-1">Auth: {issue.authUser.email} (ID: {issue.authUser.id})</p>
                  )}
                  
                  {issue.employee && (
                    <p className="text-xs mt-1">
                      Employee: {issue.employee.first_name} {issue.employee.last_name} ({issue.employee.email})
                    </p>
                  )}
                </div>
                
                {issue.type === 'orphaned_employee' && issue.employee && (
                  <button
                    onClick={() => createAuthUserForEmployee(issue.employee!)}
                    className="px-3 py-1 bg-orange-600 text-white rounded text-sm hover:bg-orange-700"
                  >
                    Create Auth User
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {syncIssues.length === 0 && !loading && (
        <div className="text-center py-8">
          <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900">All Systems Synchronized</h3>
          <p className="text-gray-600">Auth system and employee records are perfectly in sync!</p>
        </div>
      )}
    </div>
  );
};

export default AuthSyncManager;

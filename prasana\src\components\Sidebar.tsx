import React from 'react';
import { TeamName } from '../types';
import {
  Network,
  Clock,
  UserCircle,
  Shield,
  LayoutDashboard,
  BarChart2,
  FolderKanban,
  UserCheck,
  Users,
  GitBranch,
  UserPlus
} from 'lucide-react';
import { useUser } from '../contexts/UserContext';

const teamColorClasses: Record<TeamName, string> = {
  'TITAN': 'bg-blue-500',
  'NEXUS': 'bg-purple-500',
  'ATHENA': 'bg-green-500',
  'DYNAMIX': 'bg-amber-500',
  'DEVELOPMENT': 'bg-indigo-500'
};

interface SidebarProps {
  currentTeam: TeamName | null;
  setCurrentTeam: (team: TeamName | null) => void;
  currentPage: 'dashboard' | 'analytics' | 'projects' | 'collaboration' | 'timesheet' | 'team-members' | 'team-directory' | 'hrms' | 'hr-dashboard';
  setCurrentPage: (page: 'dashboard' | 'analytics' | 'projects' | 'collaboration' | 'timesheet' | 'team-members' | 'team-directory' | 'hrms' | 'hr-dashboard') => void;
  isOpen: boolean;
  onAdminLogin: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ 
  currentTeam, 
  setCurrentTeam,
  currentPage,
  setCurrentPage,
  isOpen,
  onAdminLogin
}) => {
  const { isAdmin, isSuperAdmin, currentUser } = useUser();

  const handlePageClick = (page: 'dashboard' | 'analytics' | 'projects' | 'collaboration' | 'timesheet' | 'team-members' | 'team-directory' | 'hrms' | 'hr-dashboard' | 'user-management') => {
    setCurrentPage(page);
    // Close sidebar on mobile after clicking a link
    if (window.innerWidth < 768) { // Tailwind's md breakpoint is 768px
      const appContent = document.getElementById('app-content');
      if (appContent) {
        appContent.click(); // Trigger the click handler on the content to close sidebar
      }
    }
  };

  const handleTeamClick = (team: TeamName) => {
    setCurrentTeam(currentTeam === team ? null : team);
    // Optional: Close sidebar on mobile after clicking a team filter
    // if (window.innerWidth < 768) {
    //   const appContent = document.getElementById('app-content');
    //   if (appContent) {
    //     appContent.click();
    //   }
    // }
  };

  const teams: TeamName[] = ['TITAN', 'NEXUS', 'ATHENA', 'DYNAMIX', 'DEVELOPMENT'];

  const navigationItems = [
    {
      name: 'Dashboard',
      icon: <LayoutDashboard size={18} />,
      page: 'dashboard' as 'dashboard'
    },
    {
      name: 'Analytics',
      icon: <BarChart2 size={18} />,
      page: 'analytics' as 'analytics'
    },
    {
      name: 'Projects',
      icon: <FolderKanban size={18} />,
      page: 'projects' as 'projects'
    },
    {
      name: 'HRMS',
      icon: <UserCheck size={18} />,
      page: 'hrms' as 'hrms'
    },
  ];

  return (
    <aside
      className={`${isOpen ? 'translate-x-0' : '-translate-x-full'} md:translate-x-0 fixed inset-y-0 left-0 z-20 transition-transform duration-300 ease-in-out w-64 sidebar-gradient text-white transform md:relative md:z-0 flex flex-col shadow-2xl touch-pan-y`}
    >
      <div className="flex flex-col h-full">
        <div className="p-4 sm:p-6 border-b border-blue-300/30">
          <h2 className="text-lg sm:text-xl font-bold text-white">Technosprint</h2>
          <p className="text-xs sm:text-sm text-blue-100">Project Dashboard</p>
        </div>

        <nav className="flex-1 overflow-y-auto py-4 sidebar-nav">
          <ul className="px-4 space-y-2">
            {navigationItems.map((item) => (
              <li key={item.page}>
                <button
                  onClick={() => handlePageClick(item.page)}
                  className={`sidebar-item w-full flex items-center space-x-3 px-3 sm:px-4 py-3 sm:py-3 rounded-lg min-h-[44px] touch-manipulation ${
                    currentPage === item.page
                      ? 'active bg-white/20 text-white shadow-lg backdrop-blur-sm'
                      : 'text-blue-50 hover:bg-white/10 hover:text-white hover:backdrop-blur-sm active:bg-white/15'
                  }`}
                >
                  <span className="flex-shrink-0">{item.icon}</span>
                  <span className="font-medium text-sm sm:text-base truncate">{item.name}</span>
                </button>
              </li>
            ))}
            <li>
              <button
                onClick={() => handlePageClick('team-members')}
                className={`sidebar-item w-full flex items-center space-x-3 px-3 sm:px-4 py-3 rounded-lg min-h-[44px] touch-manipulation ${
                  currentPage === 'team-members'
                    ? 'active bg-white/20 text-white shadow-lg backdrop-blur-sm'
                    : 'text-blue-50 hover:bg-white/10 hover:text-white hover:backdrop-blur-sm active:bg-white/15'
                }`}
              >
                <UserCircle size={18} className="flex-shrink-0" />
                <span className="font-medium text-sm sm:text-base truncate">Team Members</span>
              </button>
            </li>
            <li>
              <button
                onClick={() => handlePageClick('team-directory')}
                className={`sidebar-item w-full flex items-center space-x-3 px-3 sm:px-4 py-3 rounded-lg min-h-[44px] touch-manipulation ${
                  currentPage === 'team-directory'
                    ? 'active bg-white/20 text-white shadow-lg backdrop-blur-sm'
                    : 'text-blue-50 hover:bg-white/10 hover:text-white hover:backdrop-blur-sm active:bg-white/15'
                }`}
              >
                <GitBranch size={18} className="flex-shrink-0" />
                <span className="font-medium text-sm sm:text-base truncate">Team Directory</span>
              </button>
            </li>
            <li>
              <button
                onClick={() => handlePageClick('collaboration')}
                className={`sidebar-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg ${
                  currentPage === 'collaboration'
                    ? 'active bg-white/20 text-white shadow-lg backdrop-blur-sm'
                    : 'text-blue-50 hover:bg-white/10 hover:text-white hover:backdrop-blur-sm'
                }`}
              >
                <Network size={18} />
                <span className="font-medium">Team Collaboration</span>
              </button>
            </li>
            <li>
              <button
                onClick={() => handlePageClick('timesheet')}
                className={`sidebar-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg ${
                  currentPage === 'timesheet'
                    ? 'active bg-white/20 text-white shadow-lg backdrop-blur-sm'
                    : 'text-blue-50 hover:bg-white/10 hover:text-white hover:backdrop-blur-sm'
                }`}
              >
                <Clock size={18} />
                <span className="font-medium">Timesheet</span>
              </button>
            </li>
            {isSuperAdmin && (
              <>
                <li>
                  <button
                    onClick={() => handlePageClick('hr-dashboard')}
                    className={`sidebar-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg ${
                      currentPage === 'hr-dashboard'
                        ? 'active bg-white/20 text-white shadow-lg backdrop-blur-sm'
                        : 'text-blue-50 hover:bg-white/10 hover:text-white hover:backdrop-blur-sm'
                    }`}
                  >
                    <Users size={18} />
                    <span className="font-medium">HR Dashboard</span>
                  </button>
                </li>
                {/* User Management - Hidden as requested */}
                {/* <li>
                  <button
                    onClick={() => handlePageClick('user-management')}
                    className={`sidebar-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg ${
                      currentPage === 'user-management'
                        ? 'active bg-white/20 text-white shadow-lg backdrop-blur-sm'
                        : 'text-blue-50 hover:bg-white/10 hover:text-white hover:backdrop-blur-sm'
                    }`}
                  >
                    <UserPlus size={18} />
                    <span className="font-medium">User Management</span>
                  </button>
                </li> */}
              </>
            )}

          </ul>

          <div className="mt-6 sm:mt-8 px-3 sm:px-4">
            <h3 className="text-xs font-semibold text-blue-200 uppercase tracking-wider mb-3 px-2 sm:px-4">Teams</h3>
            <ul className="space-y-1 sm:space-y-2">
              {teams.map((team) => (
                <li key={team} className="flex items-center">
                  <button
                    onClick={() => handleTeamClick(team)}
                    className={`sidebar-item w-full flex items-center space-x-3 px-3 sm:px-4 py-2.5 rounded-lg min-h-[44px] touch-manipulation ${
                      currentTeam === team
                        ? 'active bg-white/20 text-white shadow-lg backdrop-blur-sm'
                        : 'text-blue-50 hover:bg-white/10 hover:text-white hover:backdrop-blur-sm active:bg-white/15'
                    }`}
                  >
                    <span className={`team-dot w-3 h-3 rounded-full flex-shrink-0 ${teamColorClasses[team]}`}></span>
                    <span className="font-medium text-sm sm:text-base truncate">{team}</span>
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </nav>

        {/* Admin Login Button - Fixed at bottom */}
        <div className="mt-auto p-4 border-t border-blue-300/30">
          <button
            onClick={onAdminLogin}
            className={`w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg border transition-all duration-200 ${
              isAdmin
                ? 'bg-green-500 hover:bg-green-600 text-white border-green-400 shadow-lg'
                : 'bg-white/10 hover:bg-white/20 text-blue-50 hover:text-white border-blue-300/50 hover:shadow-lg backdrop-blur-sm'
            }`}
          >
            <Shield size={18} />
            <span className="font-medium">{isAdmin ? 'Admin Mode' : 'Admin Login'}</span>
          </button>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
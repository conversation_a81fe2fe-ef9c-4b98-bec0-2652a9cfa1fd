import React from 'react';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Award, 
  Users, 
  Crown,
  MessageCircle,
  ExternalLink
} from 'lucide-react';
import { TeamMember } from '../../types/team';
import { DEPARTMENT_COLORS } from '../../types/teamDirectory';
import '../../styles/EmployeeManagement.css';

interface EmployeeCardProps {
  employee: TeamMember;
  onClick: () => void;
}

const EmployeeCard: React.FC<EmployeeCardProps> = ({ employee, onClick }) => {
  const departmentColor = DEPARTMENT_COLORS[employee.department as keyof typeof DEPARTMENT_COLORS] || '#6B7280';
  
  const handleEmailClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (employee.email) {
      window.location.href = `mailto:${employee.email}`;
    }
  };

  const handlePhoneClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (employee.phone) {
      window.location.href = `tel:${employee.phone}`;
    }
  };

  const handleMessageClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: Implement messaging functionality
    console.log('Message functionality to be implemented');
  };

  return (
    <div onClick={onClick}
      className="employee-card card-glow glass-morphism rounded-xl border border-gray-200 transition-all duration-300 cursor-pointer group overflow-hidden shadow-lg hover:shadow-2xl animate-fadeIn" style={{background: 'rgba(255,255,255,0.7)', backdropFilter: 'blur(12px)', boxShadow: '0 8px 32px 0 rgba(31,38,135,0.10)', border: '1px solid rgba(255,255,255,0.18)'}}
    >
      {/* Department Color Bar */}
      <div className="h-1 w-full" style={{ background: `linear-gradient(90deg, , #6366f1 80%)` }}
      />
      
      <div className="p-6">
        {/* Profile Section */}
        <div className="flex items-center space-x-4 mb-4">
          <div className="relative">
            {employee.avatar ? (
              <img 
                src={employee.avatar} 
                alt={employee.name}
                className="w-16 h-16 rounded-full object-cover border-2 border-gray-100"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  target.nextElementSibling?.classList.remove('hidden');
                }}
              />
            ) : null}
            <div 
              className={`w-16 h-16 rounded-full flex items-center justify-center text-white font-semibold text-lg border-2 border-gray-100  status-online`}
              style={{ backgroundColor: departmentColor }}
            >
              {employee.initials || employee.name.charAt(0)}
            </div>
            
            {/* Leadership Badge */}
            {employee.isLeadership && (
                <div className="absolute -top-1 -right-1 bg-gradient-to-tr from-yellow-400 via-orange-400 to-red-400 rounded-full p-1 animate-pulse-glow shadow-lg">
                  <Crown className="w-3 h-3 text-yellow-800" />
                </div>
              )}
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold gradient-text truncate group-hover:text-blue-600 transition-colors">
              {employee.name}
            </h3>
            <p className="text-sm text-gray-600 truncate">
              {employee.designation}
            </p>
            <div className="flex items-center space-x-1 mt-1">
              <span 
                className="inline-block w-2 h-2 rounded-full"
                style={{ backgroundColor: departmentColor }}
              />
              <span className="text-xs text-gray-500 truncate">
                {employee.department}
              </span>
            </div>
          </div>
        </div>

        {/* Employee Code */}
        {employee.employeeCode && (
          <div className="mb-3">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              ID: {employee.employeeCode}
            </span>
          </div>
        )}

        {/* Contact Information */}
        <div className="space-y-2 mb-4">
          {employee.email && (
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Mail className="w-4 h-4 flex-shrink-0" />
              <span className="truncate">{employee.email}</span>
            </div>
          )}
          
          {employee.phone && (
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Phone className="w-4 h-4 flex-shrink-0" />
              <span className="truncate">{employee.phone}</span>
            </div>
          )}
          
          {employee.location && (
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <MapPin className="w-4 h-4 flex-shrink-0" />
              <span className="truncate">{employee.location}</span>
            </div>
          )}
          
          {employee.joiningDate && (
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Calendar className="w-4 h-4 flex-shrink-0" />
              <span className="truncate">
                Joined {new Date(employee.joiningDate).toLocaleDateString()}
              </span>
            </div>
          )}
        </div>

        {/* Badges */}
        {employee.badges && employee.badges.length > 0 && (
          <div className="mb-4">
            <div className="flex items-center space-x-1 mb-2">
              <Award className="w-4 h-4 text-gray-500" />
              <span className="text-xs font-medium text-gray-700">Badges</span>
            </div>
            <div className="flex flex-wrap gap-1">
              {employee.badges.slice(0, 3).map((badge) => (
                <span
                  key={badge.id}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  title={badge.description}
                >
                  {badge.name}
                </span>
              ))}
              {employee.badges.length > 3 && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                  +{employee.badges.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Direct Reports */}
        {employee.directReports && employee.directReports.length > 0 && (
          <div className="mb-4">
            <div className="flex items-center space-x-1 mb-2">
              <Users className="w-4 h-4 text-gray-500" />
              <span className="text-xs font-medium text-gray-700">
                Team ({employee.directReports.length})
              </span>
            </div>
            <div className="text-xs text-gray-600">
              {employee.directReports.slice(0, 2).map(report => report.name).join(', ')}
              {employee.directReports.length > 2 && ` +${employee.directReports.length - 2} more`}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="flex space-x-2">
            {employee.email && (
              <button
                onClick={handleEmailClick}
                className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                title="Send Email"
              >
                <Mail className="w-4 h-4" />
              </button>
            )}
            
            {employee.phone && (
              <button
                onClick={handlePhoneClick}
                className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                title="Call"
              >
                <Phone className="w-4 h-4" />
              </button>
            )}
            
            <button
              onClick={handleMessageClick}
              className="p-2 text-gray-400 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors"
              title="Send Message"
            >
              <MessageCircle className="w-4 h-4" />
            </button>
          </div>
          
          <button
            onClick={onClick}
            className="flex items-center space-x-1 text-xs text-blue-600 hover:text-blue-800 font-medium"
          >
            <span>View Profile</span>
            <ExternalLink className="w-3 h-3" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default EmployeeCard;

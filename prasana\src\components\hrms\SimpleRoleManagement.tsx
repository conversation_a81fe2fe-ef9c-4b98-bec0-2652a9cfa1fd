import React, { useState, useEffect } from 'react';
import { 
  Shield, 
  Users, 
  Plus, 
  Edit, 
  Trash2, 
  Check, 
  X,
  User<PERSON><PERSON><PERSON>,
  UserX,
  Set<PERSON>s,
  Eye,
  Search,
  AlertTriangle,
  Refresh<PERSON><PERSON>,
  Star
} from 'lucide-react';
import { useHRMS } from '../../contexts/HRMSContext';
import { supabase } from '../../supabaseClient';
import RolePermissionTester from './RolePermissionTester';
import AuthenticationTester from './AuthenticationTester';

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  is_active: boolean;
  role_level?: number;
  created_at: string;
}

interface EmployeeRole {
  id: string;
  employee_id: string;
  role_id: string;
  assigned_date: string;
  is_active: boolean;
  approval_status?: string;
  expiry_date?: string;
  role?: Role;
  employee?: {
    first_name: string;
    last_name: string;
    email: string;
    designation: string;
  };
}

interface PermissionGroup {
  id: string;
  name: string;
  description: string;
  category: string;
  permissions: string[];
  color: string;
  icon: string;
}

const SimpleRoleManagement: React.FC = () => {
  const { currentEmployee } = useHRMS();
  const [roles, setRoles] = useState<Role[]>([]);
  const [employeeRoles, setEmployeeRoles] = useState<EmployeeRole[]>([]);
  const [permissionGroups, setPermissionGroups] = useState<PermissionGroup[]>([]);
  const [employees, setEmployees] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'my-roles' | 'roles' | 'assignments' | 'groups' | 'test' | 'auth'>('my-roles');
  const [showCreateRole, setShowCreateRole] = useState(false);
  const [showAssignRole, setShowAssignRole] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const [newRole, setNewRole] = useState({
    name: '',
    description: '',
    permissions: [] as string[],
    role_level: 1
  });

  const [assignRole, setAssignRole] = useState({
    employee_id: '',
    role_id: '',
    expiry_date: ''
  });

  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [showEditRole, setShowEditRole] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [showBulkAssign, setShowBulkAssign] = useState(false);
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([]);
  const [bulkRoleId, setBulkRoleId] = useState('');

  // Clear success message after 3 seconds
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => setSuccessMessage(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  // Simplified permission system with logical groupings
  const availablePermissions = [
    // Core Access
    'employee_access',      // Can view employee profiles and basic info
    'leave_access',         // Can view and manage own leaves
    'timesheet_access',     // Can manage own timesheets
    'document_access',      // Can view and upload own documents
    'performance_access',   // Can view own performance data

    // Management Permissions
    'team_management',      // Can manage team members (view/edit team data)
    'leave_approval',       // Can approve/reject team leave requests
    'performance_review',   // Can conduct performance reviews
    'document_approval',    // Can approve team documents

    // HR Permissions
    'hr_management',        // Full employee lifecycle management
    'payroll_management',   // Payroll processing and management
    'policy_management',    // Company policies and procedures

    // Administrative
    'role_management',      // Manage roles and permissions
    'system_reports',       // Generate and export system reports
    'admin_access'          // Full system administration
  ];

  useEffect(() => {
    if (currentEmployee) {
      loadRoleData();
    }
  }, [currentEmployee]);

  const loadRoleData = async () => {
    try {
      setLoading(true);
      setError(null);
      await Promise.all([
        loadRoles(),
        loadEmployeeRoles(),
        loadPermissionGroups(),
        loadEmployees()
      ]);
    } catch (error) {
      console.error('Error loading role data:', error);
      setError((error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  const loadRoles = async () => {
    const { data, error } = await supabase
      .from('roles')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) throw error;
    setRoles(data || []);
  };

  const loadEmployeeRoles = async () => {
    const { data, error } = await supabase
      .from('employee_roles')
      .select('*')
      .eq('is_active', true)
      .order('assigned_date', { ascending: false });

    if (error) throw error;
    
    // Manually enrich with role and employee data
    const enrichedRoles = [];
    for (const er of data || []) {
      const { data: role } = await supabase
        .from('roles')
        .select('*')
        .eq('id', er.role_id)
        .single();

      const { data: employee } = await supabase
        .from('employees')
        .select('first_name, last_name, email, designation')
        .eq('id', er.employee_id)
        .single();

      enrichedRoles.push({
        ...er,
        role: role || {},
        employee: employee || {}
      });
    }

    setEmployeeRoles(enrichedRoles);
  };

  const loadPermissionGroups = async () => {
    try {
      const { data, error } = await supabase
        .from('permission_groups')
        .select('*')
        .eq('is_active', true)
        .order('sort_order');

      if (!error && data) {
        setPermissionGroups(data);
      }
    } catch (error) {
      console.warn('Permission groups table may not exist yet');
    }
  };

  const loadEmployees = async () => {
    const { data, error } = await supabase
      .from('employees')
      .select('id, first_name, last_name, email, designation')
      .eq('status', 'active')
      .order('first_name');

    if (error) throw error;
    setEmployees(data || []);
  };

  const handleCreateRole = async () => {
    if (!newRole.name.trim()) {
      setError('Please enter a role name');
      return;
    }

    try {
      const { error } = await supabase
        .from('roles')
        .insert([{
          name: newRole.name,
          description: newRole.description,
          permissions: newRole.permissions,
          role_level: newRole.role_level || 1,
          is_active: true,
          created_by: currentEmployee?.id
        }]);

      if (!error) {
        setNewRole({ name: '', description: '', permissions: [], role_level: 1 });
        setShowCreateRole(false);
        setSuccessMessage('Role created successfully!');
        loadRoles();
      } else {
        if (error.message.includes('row-level security')) {
          setError('Permission denied: You may not have permission to create roles. Please contact your administrator.');
        } else if (error.message.includes('duplicate key')) {
          setError('A role with this name already exists. Please choose a different name.');
        } else {
          setError('Failed to create role: ' + error.message);
        }
      }
    } catch (error) {
      console.error('Error creating role:', error);
      setError('An unexpected error occurred while creating the role');
    }
  };

  const handleAssignRole = async () => {
    if (!assignRole.employee_id || !assignRole.role_id) {
      setError('Please select both employee and role');
      return;
    }

    try {
      // Check if employee already has this role
      const { data: existingRole } = await supabase
        .from('employee_roles')
        .select('id')
        .eq('employee_id', assignRole.employee_id)
        .eq('role_id', assignRole.role_id)
        .eq('is_active', true)
        .single();

      if (existingRole) {
        setError('Employee already has this role assigned');
        return;
      }

      const { error } = await supabase
        .from('employee_roles')
        .insert([{
          employee_id: assignRole.employee_id,
          role_id: assignRole.role_id,
          assigned_by: currentEmployee?.id,
          approval_status: 'approved',
          expiry_date: assignRole.expiry_date || null
        }]);

      if (!error) {
        setAssignRole({ employee_id: '', role_id: '', expiry_date: '' });
        setShowAssignRole(false);
        setSuccessMessage('Role assigned successfully!');
        loadEmployeeRoles();
      } else {
        if (error.message.includes('row-level security')) {
          setError('Permission denied: You may not have permission to assign roles. Please contact your administrator.');
        } else if (error.message.includes('duplicate key') || error.message.includes('unique')) {
          setError('This role assignment already exists or conflicts with an existing assignment.');
        } else {
          setError('Failed to assign role: ' + error.message);
        }
      }
    } catch (error) {
      console.error('Error assigning role:', error);
      setError('An unexpected error occurred while assigning the role');
    }
  };

  const handleEditRole = (role: Role) => {
    setEditingRole(role);
    setNewRole({
      name: role.name,
      description: role.description,
      permissions: [...role.permissions],
      role_level: role.role_level || 1
    });
    setShowEditRole(true);
  };

  const handleUpdateRole = async () => {
    if (!editingRole || !newRole.name.trim()) return;

    try {
      const { error } = await supabase
        .from('roles')
        .update({
          name: newRole.name,
          description: newRole.description,
          permissions: newRole.permissions,
          updated_at: new Date().toISOString()
        })
        .eq('id', editingRole.id);

      if (!error) {
        setNewRole({ name: '', description: '', permissions: [], role_level: 1 });
        setEditingRole(null);
        setShowEditRole(false);
        loadRoles();
      }
    } catch (error) {
      console.error('Error updating role:', error);
    }
  };

  const handleDeleteRole = async (roleId: string) => {
    if (!confirm('Are you sure you want to delete this role? This action cannot be undone.')) return;

    try {
      // Check if role has active assignments
      const { data: assignments } = await supabase
        .from('employee_roles')
        .select('id')
        .eq('role_id', roleId)
        .eq('is_active', true);

      if (assignments && assignments.length > 0) {
        alert('Cannot delete role with active assignments. Please remove all assignments first.');
        return;
      }

      const { error } = await supabase
        .from('roles')
        .update({ is_active: false })
        .eq('id', roleId);

      if (!error) {
        loadRoles();
      }
    } catch (error) {
      console.error('Error deleting role:', error);
    }
  };

  const handleBulkAssign = async () => {
    if (selectedEmployees.length === 0 || !bulkRoleId) {
      setError('Please select employees and a role for bulk assignment');
      return;
    }

    try {
      let successCount = 0;
      let errorCount = 0;

      for (const employeeId of selectedEmployees) {
        // Check if employee already has this role
        const { data: existingRole } = await supabase
          .from('employee_roles')
          .select('id')
          .eq('employee_id', employeeId)
          .eq('role_id', bulkRoleId)
          .eq('is_active', true)
          .single();

        if (!existingRole) {
          const { error } = await supabase
            .from('employee_roles')
            .insert([{
              employee_id: employeeId,
              role_id: bulkRoleId,
              assigned_by: currentEmployee?.id,
              approval_status: 'approved'
            }]);

          if (!error) {
            successCount++;
          } else {
            errorCount++;
          }
        } else {
          errorCount++;
        }
      }

      setSelectedEmployees([]);
      setBulkRoleId('');
      setShowBulkAssign(false);
      setSuccessMessage(`Bulk assignment completed: ${successCount} successful, ${errorCount} skipped/failed`);
      loadEmployeeRoles();
    } catch (error) {
      console.error('Error in bulk assignment:', error);
      setError('An error occurred during bulk assignment');
    }
  };

  const toggleEmployeeSelection = (employeeId: string) => {
    setSelectedEmployees(prev =>
      prev.includes(employeeId)
        ? prev.filter(id => id !== employeeId)
        : [...prev, employeeId]
    );
  };

  const removeRole = async (employeeRoleId: string) => {
    if (!confirm('Are you sure you want to remove this role assignment?')) return;

    try {
      const { error } = await supabase
        .from('employee_roles')
        .update({ is_active: false })
        .eq('id', employeeRoleId);

      if (!error) {
        loadEmployeeRoles();
      }
    } catch (error) {
      console.error('Error removing role:', error);
    }
  };

  const togglePermission = (permission: string) => {
    setNewRole(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permission)
        ? prev.permissions.filter(p => p !== permission)
        : [...prev.permissions, permission]
    }));
  };

  const getMyRoles = () => {
    return employeeRoles.filter(er => er.employee_id === currentEmployee?.id);
  };

  const getRoleAssignmentCount = (roleId: string) => {
    return employeeRoles.filter(er => er.role_id === roleId).length;
  };

  const getExpiringRoles = () => {
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

    return employeeRoles.filter(er =>
      er.expiry_date &&
      new Date(er.expiry_date) <= thirtyDaysFromNow &&
      new Date(er.expiry_date) > new Date()
    );
  };

  const getExpiredRoles = () => {
    return employeeRoles.filter(er =>
      er.expiry_date && new Date(er.expiry_date) < new Date()
    );
  };

  const filteredEmployeeRoles = employeeRoles.filter(er => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    return (
      er.employee?.first_name?.toLowerCase().includes(searchLower) ||
      er.employee?.last_name?.toLowerCase().includes(searchLower) ||
      er.role?.name?.toLowerCase().includes(searchLower)
    );
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <span className="text-red-800 font-medium">Error loading role data</span>
          </div>
          <p className="text-red-600 mt-2">{error}</p>
          <button
            onClick={loadRoleData}
            className="mt-3 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 flex items-center space-x-2"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Retry</span>
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Success Message */}
      {successMessage && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <Check className="w-5 h-5 text-green-600" />
            <span className="text-green-800 font-medium">{successMessage}</span>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-red-600" />
              <span className="text-red-800 font-medium">{error}</span>
            </div>
            <button
              onClick={() => setError(null)}
              className="text-red-600 hover:text-red-800"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Role Management</h1>
            <p className="text-gray-600">Manage roles, permissions, and role assignments</p>
          </div>
          <button
            onClick={loadRoleData}
            className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Refresh</span>
          </button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <Shield className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">Total Roles</span>
            </div>
            <p className="text-xl font-bold text-blue-900">{roles.length}</p>
          </div>
          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <Users className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">Active Assignments</span>
            </div>
            <p className="text-xl font-bold text-green-900">{employeeRoles.length}</p>
          </div>
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <Star className="w-4 h-4 text-purple-600" />
              <span className="text-sm font-medium text-purple-800">My Roles</span>
            </div>
            <p className="text-xl font-bold text-purple-900">{getMyRoles().length}</p>
          </div>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-4 h-4 text-yellow-600" />
              <span className="text-sm font-medium text-yellow-800">Expiring Soon</span>
            </div>
            <p className="text-xl font-bold text-yellow-900">{getExpiringRoles().length}</p>
          </div>
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <Settings className="w-4 h-4 text-orange-600" />
              <span className="text-sm font-medium text-orange-800">Permission Groups</span>
            </div>
            <p className="text-xl font-bold text-orange-900">{permissionGroups.length}</p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'my-roles', name: 'My Roles', icon: UserCheck, count: getMyRoles().length },
              { id: 'roles', name: 'All Roles', icon: Shield, count: roles.length },
              { id: 'assignments', name: 'Role Assignments', icon: Users, count: employeeRoles.length },
              { id: 'groups', name: 'Permission Groups', icon: Settings, count: permissionGroups.length },
              { id: 'test', name: 'Test Permissions', icon: Eye },
              { id: 'auth', name: 'Auth Diagnostics', icon: AlertTriangle }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.name}</span>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  activeTab === tab.id
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-600'
                }`}>
                  {tab.count}
                </span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'my-roles' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">My Current Roles</h3>
              {getMyRoles().length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {getMyRoles().map((employeeRole) => (
                    <div key={employeeRole.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{employeeRole.role?.name || 'Unknown Role'}</h4>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          employeeRole.approval_status === 'approved' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {employeeRole.approval_status || 'Active'}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{employeeRole.role?.description || 'No description'}</p>
                      <div className="space-y-2">
                        <p className="text-xs font-medium text-gray-700">Permissions:</p>
                        <div className="flex flex-wrap gap-1">
                          {(employeeRole.role?.permissions || []).slice(0, 3).map((permission) => (
                            <span
                              key={permission}
                              className="bg-blue-100 text-blue-800 px-2 py-1 text-xs rounded"
                            >
                              {permission.replace('_', ' ')}
                            </span>
                          ))}
                          {(employeeRole.role?.permissions || []).length > 3 && (
                            <span className="bg-gray-100 text-gray-800 px-2 py-1 text-xs rounded">
                              +{(employeeRole.role?.permissions || []).length - 3} more
                            </span>
                          )}
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-3">
                        Assigned: {new Date(employeeRole.assigned_date).toLocaleDateString()}
                      </p>
                      {employeeRole.expiry_date && (
                        <p className="text-xs text-red-500">
                          Expires: {new Date(employeeRole.expiry_date).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <UserX className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No roles assigned yet</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'roles' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">System Roles</h3>
                <button
                  onClick={() => setShowCreateRole(true)}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>Create Role</span>
                </button>
              </div>

              {/* Create/Edit Role Form */}
              {(showCreateRole || showEditRole) && (
                <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Role Name</label>
                      <input
                        type="text"
                        value={newRole.name}
                        onChange={(e) => setNewRole({ ...newRole, name: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        placeholder="e.g., HR Manager"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                      <input
                        type="text"
                        value={newRole.description}
                        onChange={(e) => setNewRole({ ...newRole, description: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        placeholder="Role description"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Role Level</label>
                      <select
                        value={newRole.role_level}
                        onChange={(e) => setNewRole({ ...newRole, role_level: parseInt(e.target.value) })}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      >
                        <option value={1}>Level 1 - Employee</option>
                        <option value={2}>Level 2 - Senior Employee</option>
                        <option value={3}>Level 3 - Team Lead</option>
                        <option value={4}>Level 4 - Manager</option>
                        <option value={5}>Level 5 - Executive</option>
                      </select>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Permissions</label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {availablePermissions.map((permission) => (
                        <label key={permission} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={newRole.permissions.includes(permission)}
                            onChange={() => togglePermission(permission)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="text-sm text-gray-700">{permission.replace('_', ' ')}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={showEditRole ? handleUpdateRole : handleCreateRole}
                      className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg"
                    >
                      {showEditRole ? 'Update Role' : 'Create Role'}
                    </button>
                    <button
                      onClick={() => {
                        setShowCreateRole(false);
                        setShowEditRole(false);
                        setEditingRole(null);
                        setNewRole({ name: '', description: '', permissions: [], role_level: 1 });
                      }}
                      className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}

              {/* Roles List */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {roles.map((role) => (
                  <div key={role.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{role.name}</h4>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500">Level {role.role_level || 1}</span>
                        <button
                          onClick={() => handleEditRole(role)}
                          className="text-blue-600 hover:text-blue-800"
                          title="Edit Role"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteRole(role.id)}
                          className="text-red-600 hover:text-red-800"
                          title="Delete Role"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{role.description}</p>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <p className="text-xs font-medium text-gray-700">Permissions ({role.permissions.length}):</p>
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 text-xs rounded">
                          {getRoleAssignmentCount(role.id)} assigned
                        </span>
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {role.permissions.slice(0, 3).map((permission) => (
                          <span
                            key={permission}
                            className="bg-gray-100 text-gray-800 px-2 py-1 text-xs rounded"
                          >
                            {permission.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </span>
                        ))}
                        {role.permissions.length > 3 && (
                          <span className="bg-gray-100 text-gray-800 px-2 py-1 text-xs rounded">
                            +{role.permissions.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-3">
                      Created: {new Date(role.created_at).toLocaleDateString()}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'assignments' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Role Assignments</h3>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setShowBulkAssign(true)}
                    className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
                  >
                    <Users className="w-4 h-4" />
                    <span>Bulk Assign</span>
                  </button>
                  <button
                    onClick={() => setShowAssignRole(true)}
                    className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
                  >
                    <UserCheck className="w-4 h-4" />
                    <span>Assign Role</span>
                  </button>
                </div>
              </div>

              {/* Search */}
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="Search employees or roles..."
                />
              </div>

              {/* Assign Role Form */}
              {showAssignRole && (
                <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Employee</label>
                      <select
                        value={assignRole.employee_id}
                        onChange={(e) => setAssignRole({ ...assignRole, employee_id: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Select Employee</option>
                        {employees.map((emp) => (
                          <option key={emp.id} value={emp.id}>
                            {emp.first_name} {emp.last_name} - {emp.designation}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
                      <select
                        value={assignRole.role_id}
                        onChange={(e) => setAssignRole({ ...assignRole, role_id: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Select Role</option>
                        {roles.map((role) => (
                          <option key={role.id} value={role.id}>
                            {role.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Expiry Date (Optional)</label>
                      <input
                        type="date"
                        value={assignRole.expiry_date}
                        onChange={(e) => setAssignRole({ ...assignRole, expiry_date: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        min={new Date().toISOString().split('T')[0]}
                      />
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={handleAssignRole}
                      className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg"
                    >
                      Assign Role
                    </button>
                    <button
                      onClick={() => setShowAssignRole(false)}
                      className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}

              {/* Bulk Assign Form */}
              {showBulkAssign && (
                <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                  <h4 className="font-medium text-gray-900">Bulk Role Assignment</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Select Role</label>
                      <select
                        value={bulkRoleId}
                        onChange={(e) => setBulkRoleId(e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Select Role</option>
                        {roles.map((role) => (
                          <option key={role.id} value={role.id}>
                            {role.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Selected Employees ({selectedEmployees.length})
                      </label>
                      <div className="text-sm text-gray-600">
                        {selectedEmployees.length > 0
                          ? `${selectedEmployees.length} employees selected`
                          : 'Select employees from the list below'
                        }
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Select Employees</label>
                    <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-lg p-2">
                      {employees.map((emp) => (
                        <label key={emp.id} className="flex items-center space-x-2 p-1 hover:bg-gray-100 rounded">
                          <input
                            type="checkbox"
                            checked={selectedEmployees.includes(emp.id)}
                            onChange={() => toggleEmployeeSelection(emp.id)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="text-sm text-gray-700">
                            {emp.first_name} {emp.last_name} - {emp.designation}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <button
                      onClick={handleBulkAssign}
                      className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg"
                      disabled={selectedEmployees.length === 0 || !bulkRoleId}
                    >
                      Assign to {selectedEmployees.length} Employee{selectedEmployees.length !== 1 ? 's' : ''}
                    </button>
                    <button
                      onClick={() => {
                        setShowBulkAssign(false);
                        setSelectedEmployees([]);
                        setBulkRoleId('');
                      }}
                      className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}

              {/* Assignments List */}
              <div className="space-y-4">
                {filteredEmployeeRoles.map((employeeRole) => (
                  <div key={employeeRole.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="bg-blue-100 p-2 rounded-full">
                          <Users className="w-5 h-5 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">
                            {employeeRole.employee?.first_name} {employeeRole.employee?.last_name}
                          </h4>
                          <p className="text-sm text-gray-600">{employeeRole.employee?.designation}</p>
                          <p className="text-xs text-gray-500">{employeeRole.employee?.email}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <p className="font-medium text-gray-900">{employeeRole.role?.name}</p>
                          <p className="text-xs text-gray-500">
                            Assigned: {new Date(employeeRole.assigned_date).toLocaleDateString()}
                          </p>
                          {employeeRole.expiry_date && (
                            <p className="text-xs text-red-500">
                              Expires: {new Date(employeeRole.expiry_date).toLocaleDateString()}
                            </p>
                          )}
                        </div>
                        <button
                          onClick={() => removeRole(employeeRole.id)}
                          className="text-red-600 hover:text-red-800"
                          title="Remove Role"
                        >
                          <UserX className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {filteredEmployeeRoles.length === 0 && (
                <div className="text-center py-8">
                  <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No role assignments found</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'groups' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Permission Groups</h3>
              {permissionGroups.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {permissionGroups.map((group) => (
                    <div key={group.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center space-x-3 mb-3">
                        <div
                          className="w-8 h-8 rounded-full flex items-center justify-center"
                          style={{ backgroundColor: group.color + '20' }}
                        >
                          <span style={{ color: group.color }}>●</span>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{group.name}</h4>
                          <p className="text-xs text-gray-500">{group.category}</p>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{group.description}</p>
                      <div className="space-y-2">
                        <p className="text-xs font-medium text-gray-700">
                          Permissions ({group.permissions.length}):
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {group.permissions.map((permission) => (
                            <span
                              key={permission}
                              className="px-2 py-1 text-xs rounded"
                              style={{
                                backgroundColor: group.color + '20',
                                color: group.color
                              }}
                            >
                              {permission.replace('_', ' ')}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Settings className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No permission groups found</p>
                  <p className="text-sm text-gray-400 mt-2">
                    Run the role management migration to create permission groups
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'test' && (
            <RolePermissionTester />
          )}

          {activeTab === 'auth' && (
            <AuthenticationTester />
          )}
        </div>
      </div>
    </div>
  );
};

export default SimpleRoleManagement;

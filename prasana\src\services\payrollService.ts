import { supabase } from '../supabaseClient';
import {
  EmployeeSalaryStructure,
  PayrollPeriod,
  PayrollTransaction,
  TaxConfiguration,
  SalaryRevisionHistory,
  EmployeeLoan,
  PayrollCalculationInput,
  PayrollCalculationResult,
  PayrollDashboardStats,
  SalarySlip,
  PayrollProcessingStatus
} from '../types/payroll';

class PayrollService {
  // Salary Structure Management
  async getEmployeeSalaryStructure(employeeId: string): Promise<EmployeeSalaryStructure | null> {
    try {
      const { data, error } = await supabase
        .from('employee_salary_structure')
        .select('*')
        .eq('employee_id', employeeId)
        .eq('status', 'active')
        .order('effective_from', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      console.error('Error fetching salary structure:', error);
      return null;
    }
  }

  async createSalaryStructure(structure: Omit<EmployeeSalaryStructure, 'id' | 'created_at' | 'updated_at'>): Promise<EmployeeSalaryStructure> {
    try {
      const { data, error } = await supabase
        .from('employee_salary_structure')
        .insert(structure)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating salary structure:', error);
      throw error;
    }
  }

  async updateSalaryStructure(id: string, updates: Partial<EmployeeSalaryStructure>): Promise<EmployeeSalaryStructure> {
    try {
      const { data, error } = await supabase
        .from('employee_salary_structure')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating salary structure:', error);
      throw error;
    }
  }

  // Payroll Period Management
  async getPayrollPeriods(): Promise<PayrollPeriod[]> {
    try {
      const { data, error } = await supabase
        .from('payroll_periods')
        .select('*')
        .order('start_date', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching payroll periods:', error);
      return [];
    }
  }

  async getCurrentPayrollPeriod(): Promise<PayrollPeriod | null> {
    try {
      const currentDate = new Date().toISOString().split('T')[0];
      const { data, error } = await supabase
        .from('payroll_periods')
        .select('*')
        .lte('start_date', currentDate)
        .gte('end_date', currentDate)
        .order('start_date', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      console.error('Error fetching current payroll period:', error);
      return null;
    }
  }

  async createPayrollPeriod(period: Omit<PayrollPeriod, 'id' | 'created_at' | 'updated_at'>): Promise<PayrollPeriod> {
    try {
      const { data, error } = await supabase
        .from('payroll_periods')
        .insert(period)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating payroll period:', error);
      throw error;
    }
  }

  // Tax Configuration Management
  async getTaxConfigurations(): Promise<TaxConfiguration[]> {
    try {
      const { data, error } = await supabase
        .from('tax_configurations')
        .select('*')
        .eq('is_active', true)
        .order('tax_type', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching tax configurations:', error);
      return [];
    }
  }

  // Payroll Calculation Engine
  async calculatePayroll(input: PayrollCalculationInput): Promise<PayrollCalculationResult> {
    try {
      const { salary_structure, attendance_data, loan_deductions, bonus_amount = 0 } = input;

      // Calculate pro-rated basic salary based on attendance
      const attendanceRatio = attendance_data.present_days / attendance_data.working_days;
      const proRatedBasicSalary = salary_structure.basic_salary * attendanceRatio;

      // Calculate allowances (pro-rated)
      const allowances = {
        hra: salary_structure.hra * attendanceRatio,
        transport: salary_structure.transport_allowance * attendanceRatio,
        medical: salary_structure.medical_allowance * attendanceRatio,
        special: salary_structure.special_allowance * attendanceRatio,
        other: salary_structure.other_allowances * attendanceRatio
      };

      // Calculate overtime amount (if any)
      const overtimeRate = (salary_structure.basic_salary / (attendance_data.working_days * 8)) * 1.5;
      const overtime_amount = attendance_data.overtime_hours * overtimeRate;

      // Calculate gross salary
      const gross_salary = proRatedBasicSalary + 
        Object.values(allowances).reduce((sum, allowance) => sum + allowance, 0) + 
        overtime_amount + bonus_amount;

      // Calculate deductions
      const deductions = {
        provident_fund: Math.min(gross_salary * 0.12, 1800), // 12% of basic or 1800, whichever is lower
        professional_tax: this.calculateProfessionalTax(gross_salary),
        income_tax: await this.calculateIncomeTax(gross_salary),
        loan_deductions: loan_deductions,
        other_deductions: salary_structure.other_deductions * attendanceRatio
      };

      const total_deductions = Object.values(deductions).reduce((sum, deduction) => sum + deduction, 0);
      const net_salary = gross_salary - total_deductions;

      return {
        gross_salary: Math.round(gross_salary * 100) / 100,
        total_deductions: Math.round(total_deductions * 100) / 100,
        net_salary: Math.round(net_salary * 100) / 100,
        breakdown: {
          basic_salary: Math.round(proRatedBasicSalary * 100) / 100,
          allowances,
          overtime_amount: Math.round(overtime_amount * 100) / 100,
          bonus_amount,
          deductions
        }
      };
    } catch (error) {
      console.error('Error calculating payroll:', error);
      throw error;
    }
  }

  private calculateProfessionalTax(grossSalary: number): number {
    // Professional tax calculation based on salary slabs
    if (grossSalary <= 15000) return 0;
    if (grossSalary <= 20000) return 150;
    if (grossSalary <= 25000) return 200;
    return 300;
  }

  private async calculateIncomeTax(grossSalary: number): Promise<number> {
    try {
      // Simplified income tax calculation - should be enhanced based on actual tax slabs
      const annualSalary = grossSalary * 12;
      
      if (annualSalary <= 250000) return 0;
      if (annualSalary <= 500000) return (annualSalary - 250000) * 0.05 / 12;
      if (annualSalary <= 1000000) return ((annualSalary - 500000) * 0.20 + 12500) / 12;
      
      return ((annualSalary - 1000000) * 0.30 + 112500) / 12;
    } catch (error) {
      console.error('Error calculating income tax:', error);
      return 0;
    }
  }

  // Payroll Processing
  async processPayrollForPeriod(periodId: string): Promise<PayrollProcessingStatus> {
    try {
      const status: PayrollProcessingStatus = {
        period_id: periodId,
        total_employees: 0,
        processed_count: 0,
        failed_count: 0,
        status: 'initializing',
        errors: [],
        started_at: new Date().toISOString()
      };

      // Get all active employees
      const { data: employees, error: empError } = await supabase
        .from('employees')
        .select('*')
        .eq('status', 'active');

      if (empError) throw empError;

      status.total_employees = employees?.length || 0;
      status.status = 'processing';

      // Process each employee
      for (const employee of employees || []) {
        try {
          status.current_employee = employee.first_name + ' ' + employee.last_name;
          
          // Get salary structure
          const salaryStructure = await this.getEmployeeSalaryStructure(employee.id);
          if (!salaryStructure) {
            status.errors.push({
              employee_id: employee.id,
              employee_name: status.current_employee,
              error_message: 'No active salary structure found'
            });
            status.failed_count++;
            continue;
          }

          // Get attendance data (simplified - should integrate with actual attendance system)
          const attendanceData = await this.getEmployeeAttendanceForPeriod(employee.id, periodId);
          
          // Get loan deductions
          const loanDeductions = await this.getEmployeeLoanDeductions(employee.id);

          // Calculate payroll
          const calculation = await this.calculatePayroll({
            employee_id: employee.id,
            salary_structure: salaryStructure,
            attendance_data: attendanceData,
            loan_deductions: loanDeductions
          });

          // Create payroll transaction
          await this.createPayrollTransaction(periodId, employee.id, calculation, attendanceData);
          
          status.processed_count++;
        } catch (error) {
          console.error(`Error processing payroll for employee ${employee.id}:`, error);
          status.errors.push({
            employee_id: employee.id,
            employee_name: status.current_employee || 'Unknown',
            error_message: error instanceof Error ? error.message : 'Unknown error'
          });
          status.failed_count++;
        }
      }

      status.status = status.failed_count === 0 ? 'completed' : 'failed';
      status.completed_at = new Date().toISOString();

      return status;
    } catch (error) {
      console.error('Error processing payroll:', error);
      throw error;
    }
  }

  private async getEmployeeAttendanceForPeriod(employeeId: string, periodId: string) {
    // Simplified attendance calculation - should integrate with actual attendance system
    return {
      working_days: 22,
      present_days: 20,
      leave_days: 2,
      overtime_hours: 0
    };
  }

  private async getEmployeeLoanDeductions(employeeId: string): Promise<number> {
    try {
      const { data, error } = await supabase
        .from('employee_loans')
        .select('monthly_emi')
        .eq('employee_id', employeeId)
        .eq('status', 'active');

      if (error) throw error;
      
      return data?.reduce((total, loan) => total + loan.monthly_emi, 0) || 0;
    } catch (error) {
      console.error('Error fetching loan deductions:', error);
      return 0;
    }
  }

  private async createPayrollTransaction(
    periodId: string, 
    employeeId: string, 
    calculation: PayrollCalculationResult,
    attendanceData: any
  ): Promise<PayrollTransaction> {
    try {
      const transaction = {
        payroll_period_id: periodId,
        employee_id: employeeId,
        basic_salary: calculation.breakdown.basic_salary,
        hra: calculation.breakdown.allowances.hra,
        transport_allowance: calculation.breakdown.allowances.transport,
        medical_allowance: calculation.breakdown.allowances.medical,
        special_allowance: calculation.breakdown.allowances.special,
        other_allowances: calculation.breakdown.allowances.other,
        overtime_amount: calculation.breakdown.overtime_amount,
        bonus_amount: calculation.breakdown.bonus_amount,
        gross_salary: calculation.gross_salary,
        provident_fund: calculation.breakdown.deductions.provident_fund,
        professional_tax: calculation.breakdown.deductions.professional_tax,
        income_tax: calculation.breakdown.deductions.income_tax,
        other_deductions: calculation.breakdown.deductions.other_deductions,
        loan_deductions: calculation.breakdown.deductions.loan_deductions,
        total_deductions: calculation.total_deductions,
        net_salary: calculation.net_salary,
        working_days: attendanceData.working_days,
        present_days: attendanceData.present_days,
        leave_days: attendanceData.leave_days,
        overtime_hours: attendanceData.overtime_hours,
        status: 'draft' as const
      };

      const { data, error } = await supabase
        .from('payroll_transactions')
        .insert(transaction)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating payroll transaction:', error);
      throw error;
    }
  }

  // Dashboard Statistics
  async getDashboardStats(): Promise<PayrollDashboardStats> {
    try {
      const currentPeriod = await this.getCurrentPayrollPeriod();

      // Get employee count
      const { count: totalEmployees } = await supabase
        .from('employees')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'active');

      // Get processed employees for current period
      let processedEmployees = 0;
      let pendingApprovals = 0;
      let totalPayrollAmount = 0;

      if (currentPeriod) {
        const { data: transactions } = await supabase
          .from('payroll_transactions')
          .select('*')
          .eq('payroll_period_id', currentPeriod.id);

        processedEmployees = transactions?.filter(t => t.status !== 'draft').length || 0;
        pendingApprovals = transactions?.filter(t => t.status === 'draft').length || 0;
        totalPayrollAmount = transactions?.reduce((sum, t) => sum + t.net_salary, 0) || 0;
      }

      // If no transactions exist, calculate estimated payroll from salary structures
      if (totalPayrollAmount === 0) {
        const { data: salaryStructures } = await supabase
          .from('employee_salary_structure')
          .select(`
            *,
            employees!inner(id, first_name, last_name, status)
          `)
          .eq('status', 'active')
          .eq('employees.status', 'active');

        totalPayrollAmount = salaryStructures?.reduce((sum, structure) => {
          const grossSalary = structure.basic_salary + structure.hra +
            structure.transport_allowance + structure.medical_allowance +
            structure.special_allowance + structure.other_allowances;
          const netSalary = grossSalary - (structure.provident_fund +
            structure.professional_tax + structure.income_tax + structure.other_deductions);
          return sum + netSalary;
        }, 0) || 0;
      }

      const averageSalary = totalEmployees ? totalPayrollAmount / totalEmployees : 0;

      // Get previous month data for comparison
      const { data: previousPeriods } = await supabase
        .from('payroll_periods')
        .select('total_net_amount')
        .eq('status', 'completed')
        .order('end_date', { ascending: false })
        .limit(1);

      const previousMonthAmount = previousPeriods?.[0]?.total_net_amount || totalPayrollAmount * 0.95;
      const percentageChange = previousMonthAmount > 0
        ? ((totalPayrollAmount - previousMonthAmount) / previousMonthAmount) * 100
        : 0;

      return {
        current_period: currentPeriod,
        total_employees: totalEmployees || 0,
        processed_employees: processedEmployees,
        pending_approvals: pendingApprovals,
        total_payroll_amount: totalPayrollAmount,
        average_salary: averageSalary,
        monthly_comparison: {
          current_month: totalPayrollAmount,
          previous_month: previousMonthAmount,
          percentage_change: Math.round(percentageChange * 100) / 100
        }
      };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      throw error;
    }
  }

  // Salary Slip Generation
  async generateSalarySlip(employeeId: string, periodId: string): Promise<SalarySlip | null> {
    try {
      // Get employee details
      const { data: employee, error: empError } = await supabase
        .from('employees')
        .select('*')
        .eq('id', employeeId)
        .single();

      if (empError) throw empError;

      // Get payroll period
      const { data: period, error: periodError } = await supabase
        .from('payroll_periods')
        .select('*')
        .eq('id', periodId)
        .single();

      if (periodError) throw periodError;

      // Get payroll transaction
      const { data: transaction, error: transError } = await supabase
        .from('payroll_transactions')
        .select('*')
        .eq('employee_id', employeeId)
        .eq('payroll_period_id', periodId)
        .single();

      if (transError) throw transError;

      // Calculate year-to-date totals (simplified)
      const { data: ytdTransactions } = await supabase
        .from('payroll_transactions')
        .select('gross_salary, total_deductions, net_salary, income_tax')
        .eq('employee_id', employeeId);

      const ytdTotals = ytdTransactions?.reduce((acc, t) => ({
        gross_salary: acc.gross_salary + t.gross_salary,
        total_deductions: acc.total_deductions + t.total_deductions,
        net_salary: acc.net_salary + t.net_salary,
        tax_deducted: acc.tax_deducted + t.income_tax
      }), { gross_salary: 0, total_deductions: 0, net_salary: 0, tax_deducted: 0 }) ||
      { gross_salary: 0, total_deductions: 0, net_salary: 0, tax_deducted: 0 };

      return {
        employee: {
          id: employee.id,
          name: `${employee.first_name} ${employee.last_name}`,
          employee_code: employee.employee_code || '',
          designation: employee.designation,
          department: employee.department,
          bank_account: employee.bank_account,
          pan_number: employee.pan_number
        },
        payroll_period: period,
        transaction,
        calculation_breakdown: {
          gross_salary: transaction.gross_salary,
          total_deductions: transaction.total_deductions,
          net_salary: transaction.net_salary,
          breakdown: {
            basic_salary: transaction.basic_salary,
            allowances: {
              hra: transaction.hra,
              transport: transaction.transport_allowance,
              medical: transaction.medical_allowance,
              special: transaction.special_allowance,
              other: transaction.other_allowances
            },
            overtime_amount: transaction.overtime_amount,
            bonus_amount: transaction.bonus_amount,
            deductions: {
              provident_fund: transaction.provident_fund,
              professional_tax: transaction.professional_tax,
              income_tax: transaction.income_tax,
              loan_deductions: transaction.loan_deductions,
              other_deductions: transaction.other_deductions
            }
          }
        },
        year_to_date: ytdTotals
      };
    } catch (error) {
      console.error('Error generating salary slip:', error);
      return null;
    }
  }
}

export const payrollService = new PayrollService();

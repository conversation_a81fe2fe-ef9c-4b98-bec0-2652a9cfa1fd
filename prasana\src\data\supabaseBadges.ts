import { supabase } from '../supabaseClient';
import { BadgeType, EmployeeBadge, BadgeAssignment, BadgeStats } from '../types/badge';

// Fetch all available badge types
export const fetchBadgeTypes = async (): Promise<BadgeType[]> => {
  try {
    const { data, error } = await supabase
      .from('badges')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching badge types:', error);
      throw error;
    }

    // Map badge names to actual image files in public/profiles/
    const getBadgeImagePath = (badgeName: string): string => {
      const badgeImageMap: { [key: string]: string } = {
        'IT Support Professional': '/profiles/it support.jpg',
        'Web Development Professional': '/profiles/web dev.jpg',
        'Office 365 Professional': '/profiles/office 365.jpg',
        'Full Stack Development': '/profiles/full stack.jpg',
        'Client Acquisition': '/profiles/client acquisition.jpg',
        'Digital Marketing Professional': '/profiles/digital marketing.jpg',
        'Data Analysis Professional': '/profiles/data analytics.jpg',
        'Cloud Deployment Specialist': '/profiles/cloud.jpg'
      };
      return badgeImageMap[badgeName] || '';
    };

    // Fallback to generated badge if image doesn't exist
    const generateBadgeImage = (badgeName: string, color: string): string => {
      const cleanName = badgeName.replace(/Professional|Specialist/g, '').trim();
      const initials = cleanName.split(' ').map(word => word[0]).join('').toUpperCase();
      return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=${color.replace('#', '')}&color=fff&size=128&format=png&bold=true&font-size=0.4`;
    };

    return data?.map(badge => ({
      id: badge.id,
      name: badge.name,
      description: badge.description || '',
      skills: [], // badges table doesn't have skills array, so empty for now
      image_path: getBadgeImagePath(badge.name) || generateBadgeImage(badge.name, badge.color || '#3B82F6'),
      color: badge.color || '#3B82F6',
      is_active: badge.is_active,
      created_at: badge.created_at,
      updated_at: badge.updated_at
    })) || [];
  } catch (error) {
    console.error('Error in fetchBadgeTypes:', error);
    return [];
  }
};

// Legacy function for backward compatibility
export async function fetchBadges() {
  return fetchBadgeTypes();
}

// Fetch badges for a specific employee
export const fetchEmployeeBadges = async (employeeId: string): Promise<EmployeeBadge[]> => {
  try {
    const { data, error } = await supabase
      .from('employee_badges')
      .select(`
        *,
        badges!badge_id(
          id,
          name,
          description,
          category,
          color,
          has_expiry,
          default_validity_months
        ),
        employees!assigned_by(
          first_name,
          last_name
        )
      `)
      .eq('employee_id', employeeId)
      .eq('status', 'active')
      .order('assigned_date', { ascending: false });

    if (error) {
      console.error('Error fetching employee badges:', error);
      throw error;
    }

    return data?.map(badge => ({
      ...badge,
      badge_type: badge.badges,
      assigned_by_employee: badge.employees
    })) || [];
  } catch (error) {
    console.error('Error in fetchEmployeeBadges:', error);
    return [];
  }
};

// Fetch all badges for a specific employee (including inactive ones for HR view)
export const fetchAllEmployeeBadges = async (employeeId: string): Promise<EmployeeBadge[]> => {
  try {
    const { data, error } = await supabase
      .from('employee_badges')
      .select(`
        *,
        badges!badge_id(
          id,
          name,
          description,
          category,
          color,
          has_expiry,
          default_validity_months
        ),
        employees!assigned_by(
          first_name,
          last_name
        )
      `)
      .eq('employee_id', employeeId)
      .order('assigned_date', { ascending: false });

    if (error) {
      console.error('Error fetching all employee badges:', error);
      throw error;
    }

    return data?.map(badge => ({
      ...badge,
      badge_type: badge.badges,
      assigned_by_employee: badge.employees
    })) || [];
  } catch (error) {
    console.error('Error in fetchAllEmployeeBadges:', error);
    return [];
  }
};

// Legacy function for backward compatibility
export const fetchUserBadges = async (userId: string) => {
  // Convert user_id to employee_id if needed
  return fetchEmployeeBadges(userId);
};

// Assign a badge to an employee (Admin/Superadmin only)
export const assignBadgeToEmployee = async (assignment: BadgeAssignment, assignedByEmail: string): Promise<boolean> => {
  try {
    // Get the employee ID for the user assigning the badge
    console.log('🎯 Assigning badge, looking up employee by email:', assignedByEmail);
    const assignedByEmployeeId = await getEmployeeIdByEmail(assignedByEmail);
    if (!assignedByEmployeeId) {
      throw new Error(`Could not find employee record for email: ${assignedByEmail}. Please ensure this email exists in the employees table.`);
    }

    // Calculate expiry date (default 90 days from now)
    const expiryDate = assignment.expiry_date ||
      new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    const { error } = await supabase
      .from('employee_badges')
      .insert({
        employee_id: assignment.employee_id,
        badge_id: assignment.badge_type_id,
        assigned_by: assignedByEmployeeId,
        assigned_date: new Date().toISOString().split('T')[0],
        expiry_date: expiryDate,
        status: 'active',
        notes: assignment.notes
      });

    if (error) {
      console.error('Error assigning badge:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in assignBadgeToEmployee:', error);
    return false;
  }
};

// Legacy function for backward compatibility
export async function assignBadge(userId: string, badgeId: string, expiryDays: number = 90) {
  const assignment: BadgeAssignment = {
    employee_id: userId,
    badge_type_id: badgeId,
    expiry_date: new Date(Date.now() + expiryDays * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  };
  return assignBadgeToEmployee(assignment, userId);
}

// Helper function to get employee ID from user email
export const getEmployeeIdByEmail = async (email: string): Promise<string | null> => {
  try {
    console.log('🔍 Looking up employee by email:', email);

    const { data, error } = await supabase
      .from('employees')
      .select('id, first_name, last_name')
      .eq('email', email)
      .single();

    if (error) {
      console.error('❌ Error fetching employee ID:', error);
      console.error('📧 Email searched:', email);
      return null;
    }

    if (data) {
      console.log('✅ Found employee:', data.first_name, data.last_name, 'ID:', data.id);
      return data.id;
    }

    console.log('❌ No employee found with email:', email);
    return null;
  } catch (error) {
    console.error('❌ Exception in getEmployeeIdByEmail:', error);
    return null;
  }
};

// Revoke a badge from an employee (Admin/Superadmin only)
export const revokeBadgeFromEmployee = async (
  badgeId: string,
  revokedByEmail: string,
  reason?: string
): Promise<boolean> => {
  try {
    // Get the employee ID for the user revoking the badge
    const revokedByEmployeeId = await getEmployeeIdByEmail(revokedByEmail);
    if (!revokedByEmployeeId) {
      throw new Error('Could not find employee record for the current user');
    }

    const { error } = await supabase
      .from('employee_badges')
      .update({
        status: 'revoked',
        revoked_by: revokedByEmployeeId,
        revoked_date: new Date().toISOString().split('T')[0],
        revocation_reason: reason
      })
      .eq('id', badgeId);

    if (error) {
      console.error('Error revoking badge:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in revokeBadgeFromEmployee:', error);
    return false;
  }
};

// Legacy function for backward compatibility
export async function removeBadge(assignmentId: string) {
  return revokeBadgeFromEmployee(assignmentId, '', 'Removed by admin');
}

// Add a new badge type (admin only)
export async function createBadge(badge: Omit<Badge, 'id'>) {
  try {
    const id = badge.name.toLowerCase().replace(/\s+/g, '_');
    const { data, error } = await supabase
      .from('badges')
      .insert([{ ...badge, id }])
      .select()
      .single();
    
    if (error) {
      console.error('Error creating badge:', error);
      throw error;
    }
    return data;
  } catch (error) {
    console.error('Exception in createBadge:', error);
    throw error;
  }
}

// Update a badge type (admin only)
export async function updateBadge(badgeId: string, updates: Partial<Badge>) {
  try {
    const { data, error } = await supabase
      .from('badges')
      .update(updates)
      .eq('id', badgeId)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating badge:', error);
      throw error;
    }
    return data;
  } catch (error) {
    console.error('Exception in updateBadge:', error);
    throw error;
  }
}

// Delete a badge type (admin only)
export async function deleteBadge(badgeId: string) {
  try {
    const { error } = await supabase
      .from('badges')
      .update({ is_active: false })
      .eq('id', badgeId);

    if (error) {
      console.error('Error deleting badge:', error);
      throw error;
    }
  } catch (error) {
    console.error('Exception in deleteBadge:', error);
    throw error;
  }
}

// Get badge statistics for an employee
export const getEmployeeBadgeStats = async (employeeId: string): Promise<BadgeStats> => {
  try {
    const { data, error } = await supabase
      .from('employee_badges')
      .select(`
        status,
        badges!badge_id(name)
      `)
      .eq('employee_id', employeeId);

    if (error) {
      console.error('Error fetching badge stats:', error);
      throw error;
    }

    const stats: BadgeStats = {
      total_badges: data?.length || 0,
      active_badges: data?.filter(b => b.status === 'active').length || 0,
      expired_badges: data?.filter(b => b.status === 'expired').length || 0,
      revoked_badges: data?.filter(b => b.status === 'revoked').length || 0,
      badges_by_type: {}
    };

    // Count badges by type
    data?.forEach(badge => {
      if (badge.badges?.name) {
        stats.badges_by_type[badge.badges.name] =
          (stats.badges_by_type[badge.badges.name] || 0) + 1;
      }
    });

    return stats;
  } catch (error) {
    console.error('Error in getEmployeeBadgeStats:', error);
    return {
      total_badges: 0,
      active_badges: 0,
      expired_badges: 0,
      revoked_badges: 0,
      badges_by_type: {}
    };
  }
};

// Check if employee already has a specific badge
export const checkEmployeeHasBadge = async (employeeId: string, badgeTypeId: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('employee_badges')
      .select('id')
      .eq('employee_id', employeeId)
      .eq('badge_id', badgeTypeId)
      .eq('status', 'active')
      .limit(1);

    if (error) {
      console.error('Error checking employee badge:', error);
      return false;
    }

    return (data?.length || 0) > 0;
  } catch (error) {
    console.error('Error in checkEmployeeHasBadge:', error);
    return false;
  }
};
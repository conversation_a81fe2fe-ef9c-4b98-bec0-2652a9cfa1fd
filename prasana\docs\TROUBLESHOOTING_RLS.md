# 🔧 Troubleshooting Role Management RLS Issues

## **Common Error: "new row violates row-level security policy"**

This error occurs when Row Level Security (RLS) policies are too restrictive and prevent role management operations.

### **Quick Fix - Run This Migration:**

```sql
-- Execute this in Supabase SQL Editor:
-- prasana/supabase/migrations/20240401_fix_rls_policies.sql
```

### **Manual Fix Steps:**

If you prefer to fix manually, run these commands in Supabase SQL Editor:

#### **1. Drop Restrictive Policies**
```sql
-- Drop existing restrictive policies
DROP POLICY IF EXISTS "Users can view roles" ON roles;
DROP POLICY IF EXISTS "Users can manage roles" ON roles;
DROP POLICY IF EXISTS "Users can create roles" ON roles;
DROP POLICY IF EXISTS "Users can update roles" ON roles;
DROP POLICY IF EXISTS "Users can delete roles" ON roles;
```

#### **2. Create Permissive Policies**
```sql
-- Create new permissive policies for roles
CREATE POLICY "Anyone can view roles" ON roles
    FOR SELECT USING (true);

CREATE POLICY "Authenticated users can create roles" ON roles
    FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Authenticated users can update roles" ON roles
    FOR UPDATE USING (auth.uid() IS NOT NULL);

CREATE POLICY "Authenticated users can delete roles" ON roles
    FOR DELETE USING (auth.uid() IS NOT NULL);
```

#### **3. Fix Employee Roles Policies**
```sql
-- Drop existing employee_roles policies
DROP POLICY IF EXISTS "Users can view employee roles" ON employee_roles;
DROP POLICY IF EXISTS "Users can manage employee roles" ON employee_roles;

-- Create new permissive policies
CREATE POLICY "Users can view all employee roles" ON employee_roles
    FOR SELECT USING (true);

CREATE POLICY "Authenticated users can assign roles" ON employee_roles
    FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Authenticated users can update role assignments" ON employee_roles
    FOR UPDATE USING (auth.uid() IS NOT NULL);

CREATE POLICY "Authenticated users can remove role assignments" ON employee_roles
    FOR DELETE USING (auth.uid() IS NOT NULL);
```

#### **4. Grant Proper Permissions**
```sql
-- Ensure proper table permissions
GRANT ALL ON roles TO authenticated;
GRANT ALL ON employee_roles TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
```

### **Verification Steps:**

#### **1. Test Role Creation**
```sql
-- Try creating a test role
INSERT INTO roles (name, description, permissions, role_level, is_active) 
VALUES ('Test Role', 'Test role for verification', ARRAY['employee_access'], 1, true);
```

#### **2. Test Role Assignment**
```sql
-- Try assigning a role (replace with actual IDs)
INSERT INTO employee_roles (employee_id, role_id, assigned_by, approval_status)
VALUES ('your-employee-id', 'role-id', 'your-employee-id', 'approved');
```

#### **3. Clean Up Test Data**
```sql
-- Remove test data
DELETE FROM employee_roles WHERE role_id IN (SELECT id FROM roles WHERE name = 'Test Role');
DELETE FROM roles WHERE name = 'Test Role';
```

### **Alternative: Temporarily Disable RLS**

If you need immediate access, you can temporarily disable RLS:

```sql
-- TEMPORARY: Disable RLS (NOT recommended for production)
ALTER TABLE roles DISABLE ROW LEVEL SECURITY;
ALTER TABLE employee_roles DISABLE ROW LEVEL SECURITY;

-- After fixing, re-enable with proper policies
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE employee_roles ENABLE ROW LEVEL SECURITY;
```

### **Understanding the Issue:**

#### **What Causes RLS Errors:**
1. **Overly Restrictive Policies** - Policies that are too specific
2. **Missing Policies** - No policies for certain operations
3. **Authentication Issues** - User not properly authenticated
4. **Permission Conflicts** - Conflicting policy conditions

#### **Best Practices:**
1. **Start Permissive** - Begin with broad policies, then restrict
2. **Test Incrementally** - Test each policy change
3. **Use auth.uid()** - Ensure user is authenticated
4. **Grant Table Permissions** - Don't forget GRANT statements

### **Debugging RLS Policies:**

#### **Check Current Policies:**
```sql
-- View current policies on roles table
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'roles';
```

#### **Check Table Permissions:**
```sql
-- Check table permissions
SELECT grantee, privilege_type 
FROM information_schema.role_table_grants 
WHERE table_name = 'roles';
```

#### **Test Authentication:**
```sql
-- Check if user is authenticated
SELECT auth.uid(), auth.role();
```

### **Common Solutions:**

#### **Error: "insufficient privilege"**
```sql
GRANT ALL ON roles TO authenticated;
GRANT ALL ON employee_roles TO authenticated;
```

#### **Error: "permission denied for table"**
```sql
-- Check if RLS is enabled
SELECT tablename, rowsecurity 
FROM pg_tables 
WHERE tablename IN ('roles', 'employee_roles');

-- If RLS is enabled but no policies exist, create them
```

#### **Error: "policy for command does not exist"**
```sql
-- Create missing policies for specific commands
CREATE POLICY "policy_name" ON table_name FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);
```

### **Prevention:**

1. **Always test policies** before deploying
2. **Use the Role Permission Tester** in the app
3. **Keep policies simple** and understandable
4. **Document policy changes**
5. **Have a rollback plan**

### **Getting Help:**

If you're still having issues:

1. **Check Supabase logs** for detailed error messages
2. **Use the Permission Tester** in the Role Management interface
3. **Verify your user has proper authentication**
4. **Check if the issue is table-specific or global**

**Remember: The goal is to balance security with functionality. Start permissive and tighten as needed!** 🔒

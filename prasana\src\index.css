/* Import Team Directory Styles */
@import './styles/TeamDirectory.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  /* Prevent horizontal scroll on mobile */
  overflow-x: hidden;
}

body {
  @apply bg-gray-50 text-gray-900;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  /* Improve text rendering on mobile */
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  /* Smooth scrolling */
  scroll-behavior: smooth;
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  body {
    font-size: 14px;
    line-height: 1.5;
  }

  /* Improve touch targets */
  button, a, input, select, textarea {
    min-height: 44px;
  }

  /* Prevent zoom on input focus */
  input, select, textarea {
    font-size: 16px;
  }

  /* Header mobile fixes */
  header {
    min-height: 60px;
  }

  /* Prevent header content collision */
  .header-left {
    min-width: 0;
    flex: 1;
  }

  .header-right {
    flex-shrink: 0;
    min-width: fit-content;
  }

  /* Mobile search improvements */
  .mobile-search-dropdown {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
}

/* Animation Styles */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInFromRight {
  from { transform: translateX(20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInFromBottom {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slideInFromRight {
  animation: slideInFromRight 0.3s ease-in-out;
}

.animate-slideInFromBottom {
  animation: slideInFromBottom 0.3s ease-in-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 8px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

.project-card {
  transition: all 0.3s ease;
}

.project-card:hover {
  transform: translateY(-4px);
}

/* Enhanced Sidebar Styling - Light Blue Theme */
.sidebar-gradient {
  background: linear-gradient(180deg, #3b82f6 0%, #60a5fa 50%, #3b82f6 100%);
}

.sidebar-item {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-item:hover {
  transform: translateX(4px) scale(1.02);
}

.sidebar-item.active {
  transform: translateX(4px) scale(1.05);
  box-shadow: 0 4px 12px rgba(96, 165, 250, 0.4);
}

/* Team color dots with glow effect */
.team-dot {
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
}

/* Smooth scrollbar for sidebar */
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: rgba(96, 165, 250, 0.1);
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(96, 165, 250, 0.3);
  border-radius: 2px;
}

/* Mobile-specific utility classes */
.touch-manipulation {
  touch-action: manipulation;
}

.touch-pan-y {
  touch-action: pan-y;
}

/* Prevent text selection on mobile for UI elements */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Mobile-friendly card layouts */
@media (max-width: 640px) {
  .mobile-card {
    @apply rounded-lg shadow-sm border border-gray-200 bg-white p-4 mb-4;
  }

  .mobile-grid {
    @apply grid grid-cols-1 gap-4;
  }

  .mobile-stack {
    @apply flex flex-col space-y-3;
  }
}

/* Tablet layouts */
@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-grid {
    @apply grid grid-cols-2 gap-4;
  }
}

/* Desktop layouts */
@media (min-width: 1025px) {
  .desktop-grid {
    @apply grid grid-cols-3 gap-6;
  }
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(96, 165, 250, 0.5);
}
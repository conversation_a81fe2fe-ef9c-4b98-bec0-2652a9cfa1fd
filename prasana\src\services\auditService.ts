import { supabase } from '../supabaseClient';

export interface AuditLogEntry {
  id?: string;
  user_id: string;
  action: string;
  table_name: string;
  record_id?: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  created_at?: string;
}

/**
 * Log profile update actions for audit trail
 */
export const logProfileUpdate = async (
  userId: string,
  action: 'UPDATE' | 'CREATE' | 'DELETE',
  tableName: 'users' | 'employees',
  recordId: string,
  oldValues?: Record<string, any>,
  newValues?: Record<string, any>
): Promise<void> => {
  try {
    const auditEntry: AuditLogEntry = {
      user_id: userId,
      action,
      table_name: tableName,
      record_id: recordId,
      old_values: oldValues,
      new_values: newValues,
      ip_address: await getClientIP(),
      user_agent: navigator.userAgent,
      created_at: new Date().toISOString()
    };

    const { error } = await supabase
      .from('audit_log')
      .insert(auditEntry);

    if (error) {
      console.error('Error logging audit entry:', error);
      // Don't throw error - audit logging failure shouldn't break the main operation
    }
  } catch (error) {
    console.error('Error in logProfileUpdate:', error);
    // Don't throw error - audit logging failure shouldn't break the main operation
  }
};

/**
 * Get client IP address (best effort)
 */
const getClientIP = async (): Promise<string> => {
  try {
    // This is a simple approach - in production you might want to use a more reliable service
    const response = await fetch('https://api.ipify.org?format=json');
    const data = await response.json();
    return data.ip || 'unknown';
  } catch {
    return 'unknown';
  }
};

/**
 * Log profile picture upload
 */
export const logProfilePictureUpload = async (
  userId: string,
  fileName: string,
  fileSize: number,
  success: boolean,
  error?: string
): Promise<void> => {
  try {
    const auditEntry: AuditLogEntry = {
      user_id: userId,
      action: success ? 'UPLOAD_SUCCESS' : 'UPLOAD_FAILED',
      table_name: 'users',
      record_id: userId,
      new_values: {
        file_name: fileName,
        file_size: fileSize,
        error: error || null
      },
      ip_address: await getClientIP(),
      user_agent: navigator.userAgent,
      created_at: new Date().toISOString()
    };

    const { error: auditError } = await supabase
      .from('audit_log')
      .insert(auditEntry);

    if (auditError) {
      console.error('Error logging profile picture upload:', auditError);
    }
  } catch (error) {
    console.error('Error in logProfilePictureUpload:', error);
  }
};

/**
 * Get audit log entries for a user (for admin/compliance purposes)
 */
export const getUserAuditLog = async (
  userId: string,
  limit: number = 50,
  offset: number = 0
): Promise<AuditLogEntry[]> => {
  try {
    const { data, error } = await supabase
      .from('audit_log')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching audit log:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getUserAuditLog:', error);
    throw error;
  }
};

/**
 * Security check: Rate limiting for profile updates
 */
export const checkRateLimit = async (
  userId: string,
  action: string,
  timeWindowMinutes: number = 60,
  maxAttempts: number = 10
): Promise<{ allowed: boolean; remainingAttempts: number }> => {
  try {
    const timeWindow = new Date();
    timeWindow.setMinutes(timeWindow.getMinutes() - timeWindowMinutes);

    const { data, error } = await supabase
      .from('audit_log')
      .select('id')
      .eq('user_id', userId)
      .eq('action', action)
      .gte('created_at', timeWindow.toISOString());

    if (error) {
      console.error('Error checking rate limit:', error);
      // If we can't check rate limit, allow the action but log the error
      return { allowed: true, remainingAttempts: maxAttempts };
    }

    const attemptCount = data?.length || 0;
    const remainingAttempts = Math.max(0, maxAttempts - attemptCount);

    return {
      allowed: attemptCount < maxAttempts,
      remainingAttempts
    };
  } catch (error) {
    console.error('Error in checkRateLimit:', error);
    // If we can't check rate limit, allow the action but log the error
    return { allowed: true, remainingAttempts: maxAttempts };
  }
};

/**
 * Security check: Validate user permissions for profile access
 */
export const validateProfileAccess = async (
  currentUserId: string,
  targetUserId: string,
  isSuperAdmin: boolean = false
): Promise<{ allowed: boolean; reason?: string }> => {
  try {
    // Super admins can access any profile
    if (isSuperAdmin) {
      return { allowed: true };
    }

    // Users can access their own profile
    if (currentUserId === targetUserId) {
      return { allowed: true };
    }

    // Check if current user is a manager of the target user
    const { data: employeeData, error } = await supabase
      .from('employees')
      .select('manager_id')
      .eq('user_id', targetUserId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error checking manager relationship:', error);
      return { allowed: false, reason: 'Unable to verify access permissions' };
    }

    if (employeeData?.manager_id) {
      const { data: managerData, error: managerError } = await supabase
        .from('employees')
        .select('user_id')
        .eq('id', employeeData.manager_id)
        .single();

      if (!managerError && managerData?.user_id === currentUserId) {
        return { allowed: true };
      }
    }

    return { allowed: false, reason: 'Insufficient permissions to access this profile' };
  } catch (error) {
    console.error('Error in validateProfileAccess:', error);
    return { allowed: false, reason: 'Unable to verify access permissions' };
  }
};

/**
 * Log security events
 */
export const logSecurityEvent = async (
  userId: string,
  event: 'UNAUTHORIZED_ACCESS' | 'RATE_LIMIT_EXCEEDED' | 'INVALID_DATA' | 'SUSPICIOUS_ACTIVITY',
  details: Record<string, any>
): Promise<void> => {
  try {
    const auditEntry: AuditLogEntry = {
      user_id: userId,
      action: `SECURITY_${event}`,
      table_name: 'security_events',
      new_values: details,
      ip_address: await getClientIP(),
      user_agent: navigator.userAgent,
      created_at: new Date().toISOString()
    };

    const { error } = await supabase
      .from('audit_log')
      .insert(auditEntry);

    if (error) {
      console.error('Error logging security event:', error);
    }
  } catch (error) {
    console.error('Error in logSecurityEvent:', error);
  }
};

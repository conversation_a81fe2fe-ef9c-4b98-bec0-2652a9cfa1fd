import React, { useState, useEffect } from 'react';
import { RefreshCw, AlertCircle } from 'lucide-react';

interface LoadingWithTimeoutProps {
  isLoading: boolean;
  timeout?: number; // in milliseconds
  onTimeout?: () => void;
  onRetry?: () => void;
  title?: string;
  description?: string;
  showRetryAfter?: number; // show retry button after this many seconds
}

const LoadingWithTimeout: React.FC<LoadingWithTimeoutProps> = ({
  isLoading,
  timeout = 15000, // 15 seconds default
  onTimeout,
  onRetry,
  title = "Loading...",
  description = "Please wait while we load your data",
  showRetryAfter = 10 // 10 seconds
}) => {
  const [hasTimedOut, setHasTimedOut] = useState(false);
  const [showRetry, setShowRetry] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0);

  useEffect(() => {
    if (!isLoading) {
      setHasTimedOut(false);
      setShowRetry(false);
      setElapsedTime(0);
      return;
    }

    const startTime = Date.now();
    
    // Update elapsed time every second
    const elapsedInterval = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    // Show retry button after specified time
    const retryTimeout = setTimeout(() => {
      setShowRetry(true);
    }, showRetryAfter * 1000);

    // Handle timeout
    const timeoutId = setTimeout(() => {
      setHasTimedOut(true);
      onTimeout?.();
    }, timeout);

    return () => {
      clearInterval(elapsedInterval);
      clearTimeout(retryTimeout);
      clearTimeout(timeoutId);
    };
  }, [isLoading, timeout, onTimeout, showRetryAfter]);

  if (!isLoading) return null;

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="text-center max-w-md p-6">
        {!hasTimedOut ? (
          <>
            {/* Loading spinner */}
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
            
            <h2 className="text-xl font-semibold text-gray-700 mb-2">{title}</h2>
            <p className="text-gray-500 mb-4">{description}</p>
            
            {/* Elapsed time */}
            <div className="text-sm text-gray-400 mb-4">
              {elapsedTime > 0 && `${elapsedTime}s elapsed`}
            </div>

            {/* Show retry button after specified time */}
            {showRetry && onRetry && (
              <div className="space-y-3">
                <p className="text-sm text-gray-600">Taking longer than expected?</p>
                <button
                  onClick={onRetry}
                  className="flex items-center justify-center space-x-2 mx-auto px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>Retry</span>
                </button>
              </div>
            )}
          </>
        ) : (
          <>
            {/* Timeout state */}
            <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertCircle className="w-8 h-8 text-orange-600" />
            </div>
            
            <h2 className="text-xl font-semibold text-gray-700 mb-2">Loading Timeout</h2>
            <p className="text-gray-500 mb-6">
              The request is taking longer than expected. This might be due to a slow connection or server issue.
            </p>

            {onRetry && (
              <div className="space-y-3">
                <button
                  onClick={onRetry}
                  className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>Try Again</span>
                </button>
                
                <button
                  onClick={() => window.location.reload()}
                  className="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Refresh Page
                </button>
              </div>
            )}

            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Tip:</strong> Try switching to another tab and back, or check your internet connection.
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default LoadingWithTimeout;

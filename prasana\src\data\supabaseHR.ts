import { supabase } from '../supabaseClient';
import {
  HREmployeeProfile,
  HREmployeeBadge,
  HRLeaveRecord,
  HREmployeeSkill,
  HREmployeeDocument,
  HRFilterOptions,
  HRSearchOptions
} from '../types/hr';

// Fetch all employee profiles for HR (Super Admin only)
export const fetchHREmployeeProfiles = async (): Promise<HREmployeeProfile[]> => {
  try {
    const { data, error } = await supabase.rpc('get_hr_employee_profiles');

    if (error) {
      console.error('Error fetching HR employee profiles:', error);
      // If function doesn't exist, provide helpful error message
      if (error.code === '42883') {
        throw new Error('HR functions not available. Please contact your system administrator.');
      }
      throw error;
    }

    // Filter out system administrator accounts from HR dashboard
    const filteredData = (data || []).filter(employee =>
      employee.employee_id !== 'EMP_SUPER_001' &&
      employee.designation !== 'System Administrator'
    );

    return filteredData;
  } catch (error) {
    console.error('Error in fetchHREmployeeProfiles:', error);
    throw error;
  }
};

// Fetch detailed badge information for an employee
export const fetchEmployeeBadgesHR = async (employeeId: string): Promise<HREmployeeBadge[]> => {
  try {
    const { data, error } = await supabase.rpc('get_employee_badges_hr', {
      employee_uuid: employeeId
    });
    
    if (error) {
      console.error('Error fetching employee badges for HR:', error);
      throw error;
    }
    
    return data || [];
  } catch (error) {
    console.error('Error in fetchEmployeeBadgesHR:', error);
    throw error;
  }
};

// Fetch leave history for an employee
export const fetchEmployeeLeaveHistoryHR = async (employeeId: string): Promise<HRLeaveRecord[]> => {
  try {
    const { data, error } = await supabase.rpc('get_employee_leave_history_hr', {
      employee_uuid: employeeId
    });
    
    if (error) {
      console.error('Error fetching employee leave history for HR:', error);
      throw error;
    }
    
    return data || [];
  } catch (error) {
    console.error('Error in fetchEmployeeLeaveHistoryHR:', error);
    throw error;
  }
};

// Fetch employee skills (if skills table exists)
export const fetchEmployeeSkillsHR = async (employeeId: string): Promise<HREmployeeSkill[]> => {
  try {
    // Check if skills tables exist first
    const { data: skillsData, error } = await supabase
      .from('employee_skills')
      .select(`
        proficiency_level,
        years_experience,
        certified,
        skills (
          name
        )
      `)
      .eq('employee_id', employeeId);
    
    if (error) {
      // If table doesn't exist, return empty array
      if (error.code === '42P01') {
        return [];
      }
      throw error;
    }
    
    return skillsData?.map(skill => ({
      skill_name: skill.skills?.name || 'Unknown',
      proficiency_level: skill.proficiency_level as any,
      years_experience: skill.years_experience,
      certified: skill.certified
    })) || [];
  } catch (error) {
    console.error('Error in fetchEmployeeSkillsHR:', error);
    return []; // Return empty array if skills functionality not implemented
  }
};

// Filter and search employee profiles
export const filterHREmployeeProfiles = (
  profiles: HREmployeeProfile[],
  filters: HRFilterOptions,
  search: HRSearchOptions
): HREmployeeProfile[] => {
  let filtered = [...profiles];
  
  // Apply search
  if (search.query.trim()) {
    const query = search.query.toLowerCase();
    filtered = filtered.filter(profile => {
      return search.fields.some(field => {
        switch (field) {
          case 'name':
            return profile.full_name.toLowerCase().includes(query);
          case 'email':
            return profile.email.toLowerCase().includes(query);
          case 'designation':
            return profile.designation?.toLowerCase().includes(query);
          case 'department':
            return profile.department?.toLowerCase().includes(query);
          case 'employee_id':
            return profile.employee_id?.toLowerCase().includes(query);
          default:
            return false;
        }
      });
    });
  }
  
  // Apply filters
  if (filters.department) {
    filtered = filtered.filter(p => p.department === filters.department);
  }
  
  if (filters.team) {
    filtered = filtered.filter(p => p.team_name === filters.team);
  }
  
  if (filters.employment_type) {
    filtered = filtered.filter(p => p.employment_type === filters.employment_type);
  }
  
  if (filters.employment_status) {
    filtered = filtered.filter(p => p.employment_status === filters.employment_status);
  }
  
  if (filters.location) {
    filtered = filtered.filter(p => p.location === filters.location);
  }
  
  if (filters.badge_count_min !== undefined) {
    filtered = filtered.filter(p => p.active_badges_count >= filters.badge_count_min!);
  }
  
  if (filters.badge_count_max !== undefined) {
    filtered = filtered.filter(p => p.active_badges_count <= filters.badge_count_max!);
  }
  
  if (filters.years_of_service_min !== undefined) {
    filtered = filtered.filter(p => p.years_of_service >= filters.years_of_service_min!);
  }
  
  if (filters.years_of_service_max !== undefined) {
    filtered = filtered.filter(p => p.years_of_service <= filters.years_of_service_max!);
  }
  
  if (filters.joining_date_from) {
    filtered = filtered.filter(p => 
      p.joining_date && p.joining_date >= filters.joining_date_from!
    );
  }
  
  if (filters.joining_date_to) {
    filtered = filtered.filter(p => 
      p.joining_date && p.joining_date <= filters.joining_date_to!
    );
  }
  
  return filtered;
};

// Get unique values for filter dropdowns
export const getHRFilterOptions = (profiles: HREmployeeProfile[]) => {
  return {
    departments: [...new Set(profiles.map(p => p.department).filter(Boolean))].sort(),
    teams: [...new Set(profiles.map(p => p.team_name).filter(Boolean))].sort(),
    employment_types: [...new Set(profiles.map(p => p.employment_type).filter(Boolean))].sort(),
    employment_statuses: [...new Set(profiles.map(p => p.employment_status).filter(Boolean))].sort(),
    locations: [...new Set(profiles.map(p => p.location).filter(Boolean))].sort(),
    managers: [...new Set(profiles.map(p => p.manager_name).filter(Boolean))].sort()
  };
};

// Export employee data to CSV
export const exportEmployeeDataToCSV = (
  profiles: HREmployeeProfile[],
  fields: (keyof HREmployeeProfile)[]
): string => {
  const headers = fields.join(',');
  const rows = profiles.map(profile => 
    fields.map(field => {
      const value = profile[field];
      // Escape commas and quotes in CSV
      if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value || '';
    }).join(',')
  );
  
  return [headers, ...rows].join('\n');
};

// Fetch employee documents for HR (Super Admin only)
export const fetchHREmployeeDocuments = async (employeeId: string): Promise<HREmployeeDocument[]> => {
  try {
    const { data, error } = await supabase
      .from('employee_documents')
      .select(`
        id,
        employee_id,
        document_name,
        document_type,
        file_path,
        file_size,
        mime_type,
        is_confidential,
        access_level,
        uploaded_by,
        uploaded_by_name,
        upload_date,
        expiry_date,
        status,
        created_at,
        updated_at
      `)
      .eq('employee_id', employeeId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching employee documents:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in fetchHREmployeeDocuments:', error);
    throw error;
  }
};

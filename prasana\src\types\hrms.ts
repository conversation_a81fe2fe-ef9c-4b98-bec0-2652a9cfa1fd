// HRMS Type Definitions

export interface Employee {
  id: string;
  user_id?: string;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  date_of_birth?: string;
  gender?: 'Male' | 'Female' | 'Other';
  marital_status?: 'Single' | 'Married' | 'Divorced' | 'Widowed';
  nationality?: string;
  
  // Job Information
  designation?: string;
  department?: string;
  team?: string;
  location?: string;
  manager_id?: string;
  employment_type?: 'Full-time' | 'Part-time' | 'Contract' | 'Intern';
  joining_date: string;
  probation_end_date?: string;
  
  // Contact Information
  personal_email?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  
  // Address
  address_line1?: string;
  address_line2?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  
  // System fields
  status: 'active' | 'inactive' | 'terminated';
  profile_picture_url?: string;
  created_at: string;
  updated_at: string;
  
  // Computed fields
  full_name?: string;
  manager?: Employee;
}

export interface EmployeeDocument {
  id: string;
  employee_id: string;
  document_type: 'ID_PROOF' | 'CERTIFICATE' | 'CONTRACT' | 'RESUME' | 'OTHER';
  document_name: string;
  file_url: string;
  file_size?: number;
  mime_type?: string;
  uploaded_by: string;
  created_at: string;
}



export interface LeaveType {
  id: string;
  name: string;
  code: string;
  description?: string;
  max_days_per_year?: number;
  carry_forward_allowed: boolean;
  requires_approval: boolean;
  created_at: string;
}

export interface EmployeeLeaveBalance {
  id: string;
  employee_id: string;
  leave_type_id: string;
  year: number;
  allocated_days: number;
  used_days: number;
  carried_forward: number;
  available_days: number;
  leave_type?: LeaveType;
  created_at: string;
  updated_at: string;
}

export interface LeaveApplication {
  id: string;
  employee_id: string;
  leave_type_id: string;
  start_date: string;
  end_date: string;
  total_days: number;
  reason?: string;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  applied_date: string;
  approved_by?: string;
  approved_date?: string;
  rejection_reason?: string;
  created_at: string;
  updated_at: string;
  
  // Joined data
  leave_type?: LeaveType;
  employee?: Employee;
  approver?: Employee;
}

export interface Holiday {
  id: string;
  name: string;
  date: string;
  type: 'public' | 'optional' | 'regional';
  description?: string;
  location?: string;
  created_at: string;
}

export interface PayrollComponent {
  name: string;
  amount: number;
  type: 'allowance' | 'deduction';
}

export interface Payroll {
  id: string;
  employee_id: string;
  pay_period_start: string;
  pay_period_end: string;
  basic_salary: number;
  allowances: PayrollComponent[];
  deductions: PayrollComponent[];
  gross_salary: number;
  tax_deduction: number;
  net_salary: number;
  status: 'draft' | 'processed' | 'paid';
  processed_date?: string;
  created_at: string;
  
  // Joined data
  employee?: Employee;
}

export interface PerformanceReview {
  id: string;
  employee_id: string;
  reviewer_id: string;
  review_period_start: string;
  review_period_end: string;
  self_review?: any; // JSON structure
  manager_review?: any; // JSON structure
  goals?: any; // JSON structure
  overall_rating?: number;
  status: 'draft' | 'submitted' | 'completed';
  created_at: string;
  updated_at: string;
  
  // Joined data
  employee?: Employee;
  reviewer?: Employee;
}

export interface SelfServiceRequest {
  id: string;
  employee_id: string;
  request_type: 'ADDRESS_CHANGE' | 'BANK_DETAILS' | 'EMERGENCY_CONTACT' | 'PERSONAL_INFO' | 'OTHER';
  current_data?: any; // JSON structure
  requested_data: any; // JSON structure
  reason?: string;
  status: 'pending' | 'approved' | 'rejected';
  submitted_date: string;
  processed_by?: string;
  processed_date?: string;
  comments?: string;
  created_at: string;
  
  // Joined data
  employee?: Employee;
  processor?: Employee;
}

export interface Notification {
  id: string;
  recipient_id: string;
  sender_id?: string;
  title: string;
  message: string;
  type: 'LEAVE_APPROVAL' | 'BIRTHDAY' | 'ANNOUNCEMENT' | 'DOCUMENT_UPLOAD' | 'PAYROLL' | 'PERFORMANCE' | 'GENERAL';
  read_status: boolean;
  action_url?: string;
  created_at: string;
  
  // Joined data
  sender?: Employee;
}

// Dashboard Widget Types
export interface DashboardWidget {
  id: string;
  title: string;
  type: 'leave_balance' | 'attendance_summary' | 'upcoming_holidays' | 'team_birthdays' | 'pending_approvals' | 'recent_payslip';
  data: any;
  size: 'small' | 'medium' | 'large';
  order: number;
}



export interface TeamMemberSummary {
  employee: Employee;
  leave_balance: EmployeeLeaveBalance[];
  pending_requests: number;
}

// Form Types
export interface EmployeeFormData {
  // Personal Information
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  date_of_birth?: string;
  gender?: string;
  marital_status?: string;
  nationality?: string;

  // Job Information
  employee_id: string;
  designation?: string;
  department?: string;
  team_id?: string; // Changed from team to team_id to match database
  location?: string;
  manager_id?: string;
  employment_type?: string;
  joining_date: string;
  
  // Contact Information
  personal_email?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  
  // Address
  address_line1?: string;
  address_line2?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
}

export interface LeaveApplicationFormData {
  leave_type_id: string;
  start_date: string;
  end_date: string;
  reason?: string;
}



// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// Filter and Search Types
export interface EmployeeFilters {
  department?: string;
  team?: string;
  status?: string;
  employment_type?: string;
  search?: string;
}



export interface LeaveFilters {
  employee_id?: string;
  leave_type_id?: string;
  status?: string;
  start_date?: string;
  end_date?: string;
}

// Role and Permission Types
export type UserRole = 'employee' | 'manager' | 'hr' | 'admin';

export interface Permission {
  resource: string;
  actions: string[]; // ['read', 'write', 'delete', 'approve']
}

export interface RolePermissions {
  role: UserRole;
  permissions: Permission[];
}

// Constants
export const EMPLOYMENT_TYPES = ['Full-time', 'Part-time', 'Contract', 'Intern'] as const;
export const GENDER_OPTIONS = ['Male', 'Female', 'Other'] as const;
export const MARITAL_STATUS_OPTIONS = ['Single', 'Married', 'Divorced', 'Widowed'] as const;
export const EMPLOYEE_STATUS_OPTIONS = ['active', 'inactive', 'terminated'] as const;
export const LEAVE_STATUS_OPTIONS = ['pending', 'approved', 'rejected', 'cancelled'] as const;

